
import { _decorator, Component, Node, Prefab, Enum, instantiate, sys, view, ResolutionPolicy, screen } from 'cc';
import { AdManager } from '../ads/AdManager';
import { weiSan } from '../common/weiSanTools';
import { PlatformManager, releaseType } from '../manager/PlatformManager';
import { PoolManager } from '../manager/PoolManager';
import { ResArr } from '../item/ResArr';
const { ccclass, property } = _decorator;

@ccclass('initGame')
export class initGame extends Component {
    @property([Prefab])
    resNodeArr: Array<Prefab> = [];
    @property({ type: Enum(releaseType) })
    rType: number = releaseType.test_TEST;
    @property
    storageKey: string = "demo_Game"

    @property
    fitWidth: number = 720;
    @property
    fitHeight: number = 1280;


    onLoad() {
        if (this.rType == releaseType.h5_weiSan) {
            this.rType = releaseType.h5_common;
        }
        PlatformManager.releaseType = this.rType;
        PlatformManager.storageKey = this.storageKey;
        PlatformManager.initPlatform();
        // AdManager.loadAds();

        this.initResNode();
        this.initScreen();
        weiSan.initLog();
    }
    /** 初始化  resNode */
    initResNode() {
        for (let i = 0; i < this.resNodeArr.length; i++) {
            let resNode = instantiate(this.resNodeArr[i]);
            this.node.addChild(resNode);
            PoolManager._ins.init(resNode.getComponent(ResArr).PrefabArr);
        }
    }
    /** 电脑端  按宽高一起适配 */
    initScreen() {
        if (this.rType != releaseType.h5_weiSan && this.rType != releaseType.h5_common) { return; }
        weiSan.log("系统OS: " + sys.os);
        // view.enableAutoFullScreen(false);
        // if( screen.fullScreen() ){
        //     screen.exitFullScreen();
        // }
        // view.setResolutionPolicy(ResolutionPolicy.FIXED_WIDTH);
        if (sys.os == sys.OS.WINDOWS) {
            view.setRealPixelResolution(this.fitWidth, this.fitHeight, ResolutionPolicy.SHOW_ALL);
        }
    }
    // start() {
    // }
}