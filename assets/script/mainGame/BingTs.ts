import { _decorator, Component, Node, v3, Vec3 } from 'cc';
import { GameModel } from '../model/GameModel';
import { AudioTools } from '../common/AudioTools';
const { ccclass, property } = _decorator;

@ccclass('BingTs')
export class BingTs extends Component {
    /**是否被触摸 */
    isCreate: boolean = false;
    /**饼的类型 */
    bingType: number = 0;
    /**是否移动走了 */
    isMove: boolean = false;
    start() {
        this.node.on(Node.EventType.TOUCH_START, this.touchFunc, this);
    }

    update(deltaTime: number) {

    }
    /**触摸函数 */
    touchFunc() {
        // 基础状态检查
        if (GameModel._ins.bgTs.isCanTouch || GameModel._ins.bgTs.isMove || this.isCreate) {
            return
        }

        // 🚫 点击频率限制检查

        // 更新最后点击时
        GameModel._ins.bgTs.isCanTouch = true;

        AudioTools._ins.playAudio("click");
        GameModel._ins.bgTs.moveFunc(this.node);
        // console.log(GameModel._ins.bgTs.isCanTouch, GameModel._ins.bgTs.isMove, this.isCreate)
    }

}

