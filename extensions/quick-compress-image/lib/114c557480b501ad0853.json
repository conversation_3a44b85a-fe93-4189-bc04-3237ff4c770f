{"name": "mozjpeg", "version": "8.0.0", "description": "mozjpeg wrapper that makes it seamlessly available as a local dependency", "license": "MIT", "repository": "imagemin/mozjpeg-bin", "type": "module", "exports": "./index.js", "bin": "cli.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava --timeout=120s", "build-linux": "docker build --tag imagemin/mozjpeg docker && docker run --rm --volume $(pwd)/vendor/linux:/src/out imagemin/mozjpeg cp cjpeg /src/out"}, "files": ["index.js", "cli.js", "lib", "vendor/source"], "keywords": ["imagemin", "jpeg", "jpg", "img", "image", "compress", "minify", "mozjpeg", "optimize"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0"}, "devDependencies": {"ava": "^3.8.0", "bin-check": "^4.1.0", "compare-size": "^3.0.0", "execa": "^5.1.1", "tempy": "^2.0.0", "xo": "^0.45.0"}, "ava": {"serial": true}}