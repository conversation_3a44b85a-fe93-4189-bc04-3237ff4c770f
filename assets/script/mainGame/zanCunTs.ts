import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('zanCunTs')
export class zanCunTs extends Component {
    /**广告节点 */
    @property(Node)
    adNode: Node = null
    @property(Node)
    /**未解锁遮罩 */
    adMask: Node = null
    /**是否解锁*/
    isLock: boolean = false;
    start() {
        this.node.on(Node.EventType.TOUCH_START, this.touchFunc, this);
    }

    update(deltaTime: number) {

    }
    touchFunc() {
        if (!this.isLock) {
            return
        }
        this.adNode.active = false
        this.adMask.active = false
        this.isLock = false
    }
}

