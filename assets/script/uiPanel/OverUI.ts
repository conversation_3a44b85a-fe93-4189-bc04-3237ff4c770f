import { _decorator, Component, Node, Label, UIOpacity, v3, director, tween } from 'cc';
import { AdManager } from '../ads/AdManager';
import { Tools } from '../common/Tools';
import { weiSan } from '../common/weiSanTools';
import { NetworkManager } from '../manager/NetworkManager';
import { osType, PlatformManager, releaseType } from '../manager/PlatformManager';
import { UIManager } from '../manager/UIManager';
import { GameModel } from '../model/GameModel';
import { WordsModel } from '../model/WordsModel';
const { ccclass, property } = _decorator;


@ccclass('OverUI')
export class OverUI extends UIManager {
    @property(Node)
    bgSpr!: Node;

    @property(Node)
    viewNode!: Node;
    @property(Label)
    scoreLab!: Label;
    @property(Label)
    maxScoreLab!: Label;
    @property(Label)
    infoText!: Label;

    @property(Node)
    moreBtn!: Node;
    @property(Node)
    againBtn!: Node;

    /** 是否可点击 按钮 */
    isClick: boolean = false;
    onLoad() {
        this.bgSpr.getComponent(UIOpacity).opacity = 0;
        this.viewNode.position.add(v3(0, 1000, 0));
        this.againBtn.scale = v3(0, 0, 0);
        // GameModel._ins.gameScore = Tools.random(100,200);
        this.initShowInfo();
        this.addBtnEvent();
        // 提交分数  和 显示插屏广告
        NetworkManager.sendGameScore(GameModel._ins.bgTs.level, 1);
        AdManager.showIntersAd();

        if (PlatformManager.osType != osType.h5) {  //非H5平台 隐藏更多游戏按钮
            this.moreBtn.active = false;
        }
    }
    // start() {
    // }
    /** 显示结束页 信息 */
    initShowInfo() {
        this.scoreLab.string = Number(GameModel._ins.bgTs.level).toString();
        let tempNum = this.getBeatItScore(GameModel._ins.gameScore, GameModel._ins.standScore, GameModel._ins.gameMaxScore);
        this.infoText.string = this.getBeatItStr(GameModel._ins.bgTs.level, tempNum, true);

        let maxScore = Tools.getStorage("gameOverMaxScore");
        if (!maxScore || maxScore <= GameModel._ins.gameScore) {
            maxScore = GameModel._ins.gameScore;
            Tools.setStorage("gameOverMaxScore", GameModel._ins.bgTs.level);
        }
        this.maxScoreLab.string = maxScore.toString();

        if (PlatformManager.osType != osType.h5) { return; }
        if (Tools.getLanguageType() == "CN" || Tools.getLanguageType() == "CHT") {
            document.title = WordsModel.getStrForLanguage("overTitle_1", "CN") + "<" + WordsModel.getStrForLanguage("gameName", "CN")
                + ">" + WordsModel.getStrForLanguage("overTitle_2", "CN") + this.getBeatItStr(GameModel._ins.bgTs.level, tempNum, false);
        } else {
            document.title = WordsModel.getStrForLanguage("overTitle_1", "EN") + "<" + WordsModel.getStrForLanguage("gameName", "EN")
                + ">" + WordsModel.getStrForLanguage("overTitle_2", "EN") + this.getBeatItStr(GameModel._ins.bgTs.level, tempNum, false);
        }
        console.log(document.title);
    }

    addBtnEvent() {
        this.againBtn.on("click", () => {
            if (!this.isClick) { return; }
            this.aginGame();
        });
        this.moreBtn.on("click", () => {
            if (!this.isClick) { return; }
            if (PlatformManager.releaseType == releaseType.test_TEST) {
                weiSan.log("测试模式 更多游戏Url: " + NetworkManager.moreGameUrl);
                return;
            }
            this.isClick = false;
            window.location.href = NetworkManager.moreGameUrl;
        });
    }
    /** 再玩一次 */
    aginGame() {
        this.isClick = false;
        // AdManager.showIntersAd();
        UIManager.CloseUI("OverUI");
        this.scheduleOnce(() => {
            director.preloadScene(GameModel._ins.mainScene, () => {
                director.loadScene(GameModel._ins.mainScene);
            });
        }, 0.2);
    }
    /**
     * 获取击败了 全球多少玩家
     * @param gameScore 分数
     * @param standScore 平均分 
     * @param maxScore  最高分
     */
    getBeatItScore(gameScore: number, standScore: number, maxScore: number) {
        if (gameScore >= maxScore) {
            return 100;
        }
        if (gameScore <= standScore) {
            let temp = (gameScore / standScore) * 80 + Tools.random(-3, 3);
            return Math.max(Math.floor(temp), 5);
        } else {
            let temp = 80 + ((gameScore - standScore) / (maxScore - standScore)) * 20 + Tools.random(-3, 3);
            return Math.min(Math.floor(temp), 99);
        }
    };
    /**
    * 获取击败了 全球多少玩家 文字  tempNum:百分之多少 是否是richText
    */
    getBeatItStr(score: number, tempNum: number, isRichText = true): string {
        var share_title = WordsModel.getStrForLanguage("overScoreInfo_0");
        if (score > 0) {
            if (isRichText) {
                share_title = WordsModel.getStrForLanguage("overScoreInfo_1") +
                    tempNum + "%" + WordsModel.getStrForLanguage("overScoreInfo_2");
            } else {
                share_title = WordsModel.getStrForLanguage("overScoreInfo_1") + tempNum + "%" + WordsModel.getStrForLanguage("overScoreInfo_2");
            }
        }
        return share_title;
    };
    // update (deltaTime: number) {
    // }
    public openUI() {
        this.bgSpr.getComponent(UIOpacity)!.opacity = 0;

        tween(this.bgSpr.getComponent(UIOpacity)).to(0.3, { opacity: 180 }).start();


        tween(this.viewNode)
            .by(0.3, { position: v3(0, -1000, 0) }, { easing: "backOut" })
            .call(() => {
                this.isClick = true;
                tween(this.againBtn).to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" }).start();
            }).start();
    }
    public closeUI() {
        tween(this.bgSpr.getComponent(UIOpacity)).to(0.2, { opacity: 0 }).start();

        tween(this.viewNode)
            .by(0.3, { position: v3(0, 100, 0) }, { easing: "backIn" })
            .call(() => {
                this.node.destroy();
            }).start();


    }
    public uiName: string;
}