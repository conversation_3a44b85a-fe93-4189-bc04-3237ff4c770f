{"package_version": 2, "version": "1.0.0", "name": "we_sane", "description": "i18n:we_sane.description", "main": "./dist/main.js", "devDependencies": {"@types/node": "^16.0.1", "typescript": "^4.3.4"}, "author": "<PERSON>", "editor": ">=3.4.0", "scripts": {"build": "tsc -b", "watch": "tsc -w"}, "contributions": {"menu": [{"path": "weSane/上线H5", "label": "打包-微伞zip", "message": "build-wesane"}, {"path": "weSane/上线H5", "label": "打包-通用zip", "message": "build-h5"}], "messages": {"build-wesane": {"methods": ["weSaneZip"]}, "build-h5": {"methods": ["commonH5Zip"]}}}}