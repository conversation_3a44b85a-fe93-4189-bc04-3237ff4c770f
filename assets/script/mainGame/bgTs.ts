import { _decorator, Color, Component, Label, Node, Sprite, tween, UI, UIOpacity, UITransform, v2, v3, Vec3, view } from 'cc';
import { Tools } from '../common/Tools';
import { GameModel } from '../model/GameModel';
import { BingTs } from './BingTs';
import { peopleTs } from './peopleTs';
import { AudioTools } from '../common/AudioTools';
import { PlatformManager } from '../manager/PlatformManager';
import { kuangTs } from './kuangTs';
const { ccclass, property } = _decorator;

@ccclass('bgTs')
export class bgTs extends Component {
    /**背景节点 */
    @property(Node)
    zhuoZiDown: Node = null;
    @property(Node)
    zhuoZiup: Node = null!;
    @property(Node)
    bg: Node = null!;
    @property(Node)
    levelLable: Node = null!;
    @property(Node)
    daLuanBtn: Node = null;
    @property(Node)
    auto: Node = null
    @property(Node)
    chongZhi: Node = null
    @property(Node)
    tipNodeMask: Node = null
    /** 屏幕高度 */
    gameHeight: number = 0;
    /** 屏幕宽度 */
    gameWidth: number = 0;
    /**存储盘子位置 */
    savePanPos: Vec3[] = [];
    /**存储暂存位置 */
    saveZancunPos: Vec3[] = [];
    /**存储人的位置 */
    savePeoplePos: Vec3[] = [];
    /**存放人 */
    savePeople: Node[] = [null, null, null];
    /**存放暂存 */
    saveZancun: Node[] = [];
    /**存放盘子 */
    savePan: Node[] = [];
    /**存放饼 */
    saveBing: Node[][] = [];
    /**存放暂存点的饼 */
    saveZancunBing: Node[][] = [];
    /**暂存区饼的动画状态 */
    zanCunAnimationState: number[] = [0, 0, 0, 0, 0, 0]
    /** */
    /**饼的摞的个数 */
    bingLuoNum: number = 0;
    /**饼的总数 */
    bingNum: number = 0;
    /**饼的颜色种类 */
    bingColor: number = 0;
    /**关卡 */
    level: number = 1;
    /**订单信息 */
    orders: { piles: { color: number, count: number }[] }[] = [];
    /**饼堆信息 */
    pileConfigurations: { colors: { type: number, count: number }[] }[] = [];
    /**是否在运动 */
    isMove: boolean = false;
    /**记录当前生成组的显示层级 */
    disPlayIndex: number[] = [];
    /**游戏状体 */
    hasWon: boolean = false
    /**人物数量 */
    peopleNum: number = 0;
    /**是否死关 */
    isDead: boolean = false
    /**是否有顾客要走 */
    isHavePeopleleave: boolean = false
    /**结束状态 */
    hasEnd: boolean = false
    /**是否可以触摸 */
    isCanTouch: boolean = false
    /**点击限制相关 */
    lastClickTime: number = 0
    clickCooldown: number = 200 // 点击冷却时间，单位毫秒
    touchResetDelay: number = 150 // 触摸状态重置延迟，单位毫秒
    /**是否有待处理的死关检查 */
    pendingDeadCheck: boolean = false
    /**是否正在等待新订单创建 */
    isWaitingForNewOrder: boolean = false
    /**等待新订单的超时回调ID */
    waitingOrderTimeoutId: number = 0
    /**正在创建中的订单数量 - 用于锁定死关检测 */
    ordersInCreation: number = 0
    /**订单创建锁定状态 - 防止在订单生成期间误判死关 */
    isOrderCreationLocked: boolean = false
    /**提示出现的位置 */
    tipPos: Vec3[] = []
    /**saveTip */
    saveTip: Node[][] = [null, null, null, null]
    saveTipFloor: Node[] = [null, null, null, null]
    saveTipColors: { colors: { type: number, count: number }[] }[] = [null, null, null, null];
    /**下一个要出现的饼索引 */
    nextBingIndex: number = 0
    /**全局交付计数器，确保交付动画有合适的间隔*/
    globalDeliveryCounter: number = 0
    /**交付检测完成状态监控*/
    isAllDeliveryCheckCompleted: boolean = false // 所有交付检测是否都已完成
    lastDeliveryCheckResult: string = ""         // 最后一次检测的结果描述
    deliveryCheckCompletedTime: number = 0       // 检测完成的时间戳
    onLoad() {
        GameModel._ins.bgTs = this
        AudioTools._ins.playBG("bgm")
        this.gameHeight = view.getVisibleSize().height;
        this.gameWidth = view.getVisibleSize().width;
        this.zhuoZiDown.setPosition(v3(0, -this.gameHeight / 2))
        this.bg.setPosition(v3(0, this.gameHeight / 2))
        let screenHeight =;
        this.zhuoZiup.setPosition(v3(0, this.gameHeight / 2 - this.bg.getComponent(UITransform).height + 100))
        this.levelLable.setPosition(v3(0, this.gameHeight / 2 - this.levelLable.getComponent(UITransform).height))
        this.bg.getComponent(UITransform).width = this.gameWidth;
        this.zhuoZiDown.getComponent(UITransform).width = this.gameWidth;
        this.zhuoZiup.getComponent(UITransform).width = this.gameWidth;
        this.zhuoZiDown.getComponent(UITransform).height += this.zhuoZiup.position.y - this.zhuoZiDown.position.y - this.zhuoZiup.getComponent(UITransform).height;
        this.zhuoZiup.setSiblingIndex(4)
        if (PlatformManager.getStorage("binglevel") != null) {
            this.level = PlatformManager.getStorage("binglevel")
        } else {
            this.level = 1
        }
        this.levelLable.children[0].getComponent(Label).string = this.level.toString()
        // this.daLuanBtn.setPosition(v3(-this.gameWidth / 2 + 150, -this.gameHeight / 2 + 150))
        this.auto.setPosition(v3(-this.gameWidth / 2 + 300, -this.gameHeight / 2 + 150))
        this.chongZhi.setPosition(v3(this.gameWidth / 2 - 300, -this.gameHeight / 2 + 150))
        this.tipNodeMask.getComponent(UITransform).width = this.gameWidth
        this.tipNodeMask.getComponent(UITransform).height = 600
        this.tipNodeMask.setPosition(v3(0, this.zhuoZiup.position.y - this.zhuoZiup.getComponent(UITransform).height - 200 - 2 * 200))
        Tools.setSpriteFrame(this.bg, "bg" + Tools.random(1, 3, true))
        // this.daLuanBtn.on(Node.EventType.TOUCH_START, this.daLuan, this)
        this.chongZhi.on(Node.EventType.TOUCH_START, this.resetTianPin, this)
        this.auto.on(Node.EventType.TOUCH_START, this.resetCaiDan, this)
    }
    start() {
        this.createPanPos();
        this.createZancunPos();
        this.createPeoplePos();
        this.createPan();
        this.createZancun();
        this.setLevel();
    }
    update(deltaTime: number) {
    }
    /**设置点击限制参数 */
    setClickLimits(cooldown: number = 200, resetDelay: number = 150) {
        this.clickCooldown = cooldown;
        this.touchResetDelay = resetDelay;
        console.log(`点击限制已设置 - 冷却时间: ${cooldown}ms, 重置延迟: ${resetDelay}ms`);
    }
    /**关卡设置 */
    setLevel() {
        this.hasWon = false
        this.bingNum = 0
        switch (this.level) {
            case 1:
                this.bingLuoNum = 10
                this.bingColor = 5; //r g b
                break;
            case 2:
                this.bingLuoNum = 13
                this.bingColor = 5;
                break;
            case 3:
                this.bingLuoNum = 15
                this.bingColor = 6
                break;
            case 4:
                this.bingLuoNum = 18
                this.bingColor = 6
                break;
            case 5:
                this.bingLuoNum = 23
                this.bingColor = 6
                break;
            case 6:
                this.bingLuoNum = 25
                this.bingColor = 7
                break;
            case 7:
                this.bingLuoNum = 30
                this.bingColor = 7
                break;
            default:
                this.bingLuoNum = 34
                this.bingColor = 8
        }
        this.orders = this.SureDingDan();
        // this.resetOrders = JSON.parse(JSON.stringify(this.orders));
        if (!this.orders || !Array.isArray(this.orders)) {
            console.error("订单生成失败，orders 为空或不是数组:", this.orders);
            this.orders = []; // 设置为空数组作为兜底
        }
        this.peopleNum = this.orders.length
        console.log(this.peopleNum)
        this.BingTypeControl(); // 使用新方法替代createBing()
        for (let i = 0; i < this.savePeoplePos.length; i++) {
            this.createPeople(this.savePeoplePos[i], i)
        }
        if (this.pileConfigurations.length > 4) {
            for (let i = 0; i < 4; i++) {
                this.createTip(0, i, this.tipPos[i], this.pileConfigurations[0].colors)
            }
        } else {
            for (let i = 0; i < this.pileConfigurations.length; i++) {
                this.createTip(0, i, this.tipPos[i], this.pileConfigurations[0].colors)
            }
        }

        // 只生成一次订单
        // this.createWin()
    }
    /**生成盘子的位置 */
    createPanPos() {
        let Wlen = (this.gameWidth - (240 * 4)) / 2
        for (let i = 0; i < 2; i++) {
            for (let j = 0; j < 4; j++) {
                let x = -this.gameWidth / 2 + Wlen + 120 + 2 + j * 240
                let y = this.zhuoZiup.position.y - this.zhuoZiup.getComponent(UITransform).height - 200 - i * 240 - 30 * i;
                let pos = new Vec3(x, y, 0);
                this.savePanPos.push(pos);
                if (i == 1) {
                    this.tipPos.push(v3(pos.x, pos.y))
                    console.log(this.tipPos)
                }
            }
        }
    }
    /**生成盘子 */
    createPan() {
        for (let i = 0; i < this.savePanPos.length; i++) {
            // 安全检查：确保位置存在
            if (!this.savePanPos[i]) {
                console.error(`创建盘子时位置 ${i} 不存在，savePanPos 长度: ${this.savePanPos.length}`);
                continue;
            }
            let pan = Tools.newPrefab("Pan")
            pan.setParent(this.node)
            pan.setPosition(this.savePanPos[i])
            pan.getComponent(UITransform).width = 200
            pan.getComponent(UITransform).height = 200
            this.savePan.push(pan)
            //@ts-extr
        }
    }
    //**交付盘子的位置 */
    createZancunPos() {
        let wLen = (this.gameWidth - (180 * 5) - (30 * 4)) / 2 + 90
        for (let i = 0; i < 5; i++) {
            let x = -this.gameWidth / 2 + wLen + i * 180 + 30 * i;
            let y = this.zhuoZiup.position.y - this.zhuoZiup.getComponent(UITransform).height / 2 + 20
            let pos = new Vec3(x, y, 0);
            this.saveZancunPos.push(pos)
        }
    }
    /**生成暂存 */
    createZancun() {
        for (let i = 0; i < this.saveZancunPos.length; i++) {
            // 安全检查：确保位置存在
            if (!this.saveZancunPos[i]) {
                console.error(`创建暂存时位置 ${i} 不存在，saveZancunPos 长度: ${this.saveZancunPos.length}`);
                continue;
            }
            let zanCun = Tools.newPrefab("zanCun")
            zanCun.setParent(this.node)
            zanCun.setPosition(v3(this.saveZancunPos[i].x, this.saveZancunPos[i].y + 5))
            this.saveZancun.push(zanCun)
            this.saveZancunBing[i] = []
            zanCun.setSiblingIndex(5)
        }
    }
    /**生成人的位置 */
    createPeoplePos() {
        let wLen = (this.gameWidth - 260 * 3 - 80 * 2) / 2 + 130
        for (let i = 0; i < 3; i++) {
            let x = -this.gameWidth / 2 + wLen + i * 260 + 80 * i;
            let y = this.zhuoZiup.position.y - 10
            let pos = new Vec3(x, y, 0);
            this.savePeoplePos.push(pos);
        }
    }
    /**生成人 */
    createPeople(pos: Vec3, index: number) {
        console.log(`createPeople 被调用，peopleNum: ${this.peopleNum}, index: ${index}`);
        if (this.peopleNum <= 0) {
            console.log("peopleNum <= 0，无法创建新顾客");
            return;
        }
        // 安全检查：确保位置参数有效
        if (!pos) {
            console.error(`创建人物时位置参数无效，index: ${index}, savePeoplePos 长度: ${this.savePeoplePos.length}`);
            return;
        }
        // 🔒 锁定订单创建状态，防止死关误判
        this.ordersInCreation++;
        this.isOrderCreationLocked = true;
        console.log(`🔒 开始创建新顾客，订单创建锁定 - 创建中订单数: ${this.ordersInCreation}`);

        let people = Tools.newPrefab("people")
        people.setParent(this.node)
        people.setPosition(pos)
        people.setSiblingIndex(2)
        this.savePeople[index] = people

        this.peopleNum--
        console.log(`新顾客人物创建完成，剩余 peopleNum: ${this.peopleNum}`);
    }
    /**确定订单 */
    SureDingDan() {
        // 剩余摞数
        let remainLuo = this.bingLuoNum;
        let orders: { piles: { color: number, count: number }[] }[] = [];
        let colorDemand: number[] = new Array(this.bingColor).fill(0);
        while (remainLuo > 0) {
            // 每组1~2摞，不能超过剩余摞数
            let pilesInOrder = Math.min(Math.floor(Math.random() * 2) + 1, remainLuo);
            let orderPiles: { color: number, count: number }[] = [];
            for (let i = 0; i < pilesInOrder; i++) {
                // 每摞3~7个
                let pileCount = Math.floor(Math.random() * 5) + 3;
                // 随机颜色
                let color = Math.floor(Math.random() * this.bingColor);
                orderPiles.push({
                    color: color,
                    count: pileCount
                });
                colorDemand[color] += pileCount;
            }
            orders.push({
                piles: orderPiles
            });
            remainLuo -= pilesInOrder;
        }
        // 随机打乱订单顺序
        for (let i = orders.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [orders[i], orders[j]] = [orders[j], orders[i]];
        }
        // 打印订单需求统计
        let totalOrder = colorDemand.reduce((a, b) => a + b, 0);
        console.log("订单需求统计（颜色:数量）:", colorDemand);
        console.log("订单总需求数量:", totalOrder);

        return orders;
    }
    /**确定类型 */
    BingTypeControl() {
        // 使用订单信息统计各个颜色的数量
        if (!this.orders || this.orders.length === 0) {
            return;
        }
        // 统计订单中各个颜色的总数
        let colorCounts = new Array(this.bingColor).fill(0);
        let totalBings = 0;
        for (let order of this.orders) {
            for (let pile of order.piles) {
                colorCounts[pile.color] += pile.count;
                totalBings += pile.count;
            }
        }
        // 打印订单统计
        console.log("订单统计（颜色:数量）:", colorCounts);
        console.log("订单总饼数:", totalBings);
        // 将计算好的总数赋值给this.bingNum
        this.bingNum = totalBings;
        // 创建饼堆配置数组
        // 根据颜色数量创建饼堆
        let pileColorStat: number[] = new Array(this.bingColor).fill(0);
        let pileTotal = 0;
        while (colorCounts.some(count => count > 0)) {
            // 每摞3-7个饼
            let minPileSize = 3;
            let maxPileSize = 7;
            let pileColors: { type: number, count: number }[] = [];
            let totalInPile = 0;
            // 按颜色数量从多到少排序
            let colorIndices = colorCounts.map((count, index) => ({ count, index }))
                .filter(item => item.count > 0)
                .sort((a, b) => b.count - a.count);
            // 根据游戏进度调整混色比例，实现难度递增
            let gameProgress = this.calculateGameProgress();
            let minNum = this.determineColorMixByProgress(gameProgress);
            let colorTypeCount = Math.min(
                minNum,
                colorIndices.length // 不能超过剩余的颜色种类
            );
            // 随机确定这一摞饼的总数量（3-7个）
            let pileSize = Math.floor(Math.random() * (maxPileSize - minPileSize + 1)) + minPileSize;
            // 选择颜色
            for (let i = 0; i < Math.min(colorTypeCount, colorIndices.length); i++) {
                let colorIndex = colorIndices[i].index;
                // 计算这种颜色在这一摞中的数量
                let countForThisColor;
                if (i === 0) {
                    // 第一种颜色（主色）占比较大
                    countForThisColor = Math.min(
                        Math.ceil(pileSize * 0.75), // 主色占60%左右
                        colorCounts[colorIndex]    // 不超过剩余数量
                    );
                } else {
                    // 其他颜色平分剩余空间
                    let remainingSpace = pileSize - totalInPile;
                    let remainingColors = colorTypeCount - i;
                    countForThisColor = Math.min(
                        Math.ceil(remainingSpace / remainingColors),
                        colorCounts[colorIndex]
                    );
                }
                // 确保至少有1个
                countForThisColor = Math.max(1, countForThisColor);
                // 如果总数已经达到目标，调整最后一种颜色的数量
                if (totalInPile + countForThisColor > pileSize) {
                    countForThisColor = pileSize - totalInPile;
                }
                if (countForThisColor > 0) {
                    pileColors.push({
                        type: colorIndex,
                        count: countForThisColor
                    });
                    colorCounts[colorIndex] -= countForThisColor;
                    pileColorStat[colorIndex] += countForThisColor;
                    pileTotal += countForThisColor;
                    totalInPile += countForThisColor;
                }
                // 如果总数已经达到目标，不再添加更多颜色
                if (totalInPile >= pileSize) {
                    break;
                }
            }
            // 添加这一摞饼的配置到数组
            if (pileColors.length > 0) {
                this.pileConfigurations.push({
                    colors: pileColors
                });
            }
        }
        // 打印 pileConfigurations 统计
        // console.log("实际生成饼统计（颜色:数量）:", pileColorStat);
        // console.log("实际生成饼总数:", pileTotal);
        // 随机打乱饼堆配置
        for (let i = this.pileConfigurations.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.pileConfigurations[i], this.pileConfigurations[j]] = [this.pileConfigurations[j], this.pileConfigurations[i]];
        }
        // 将饼分配到盘子中
        let panCount = Math.min(this.savePan.length, this.pileConfigurations.length);
        for (let panIndex = 0; panIndex < panCount; panIndex++) {
            // 使用createBing函数生成饼
            if (this.pileConfigurations.length > 0) {  // 添加检查，确保数组不为空
                this.createBing(panIndex, this.pileConfigurations[0].colors);
                // 从配置数组中剔除已生成的饼摞
                this.pileConfigurations.splice(0, 1);
            }

        }
    }
    /**计算游戏进度（0-1之间）*/
    calculateGameProgress(): number {
        // 方法1：基于已完成的饼数量
        let totalBingsGenerated = this.bingLuoNum * 5; // 假设每摞平均5个饼
        let remainingBings = this.bingNum;
        let completedBings = totalBingsGenerated - remainingBings;
        let progressByBings = completedBings / totalBingsGenerated;

        // 方法2：基于关卡等级
        let maxLevel = 20; // 假设最大关卡为20
        let progressByLevel = (this.level - 1) / (maxLevel - 1);

        // 方法3：基于已完成的订单数量
        let totalOrders = this.orders.length;
        let completedOrders = Math.max(0, totalOrders - this.savePeople.filter(p => p != null).length);
        let progressByOrders = totalOrders > 0 ? completedOrders / totalOrders : 0;

        // 综合计算进度（权重：关卡40%，订单30%，饼数量30%）
        let overallProgress = progressByLevel * 0.4 + progressByOrders * 0.3 + progressByBings * 0.3;

        // 确保进度在0-1范围内
        return Math.max(0, Math.min(1, overallProgress));
    }
    /**根据游戏进度确定混色类型*/
    determineColorMixByProgress(progress: number): number {
        // 定义三个阶段的混色概率
        let singleColorProb: number;
        let doubleColorProb: number;
        let tripleColorProb: number;
        if (progress <= 0.4) {
            // 前期：主要单色，少量双色，极少三色
            singleColorProb = 0.80;
            doubleColorProb = 0.15;
            tripleColorProb = 0.05;
        } else if (progress <= 0.7) {
            // 中期：减少单色，增加双色，少量三色
            singleColorProb = 0.5;
            doubleColorProb = 0.4;
            tripleColorProb = 0.1;
        } else {
            // 后期：进一步减少单色，增加混色难度
            singleColorProb = 0.3;
            doubleColorProb = 0.4;
            tripleColorProb = 0.3;
        }

        // 根据概率随机选择
        let random = Math.random();
        if (random <= singleColorProb) {
            return 1; // 单色
        } else if (random <= singleColorProb + doubleColorProb) {
            return 2; // 双色
        } else {
            return 3; // 三色
        }
    }
    /**生成饼 */
    createBing(index: number, colors: { type: number, count: number }[]) {
        this.saveBing[index] = [];
        let arr: Node[] = [];
        let currentHeight = 0;
        let posArr: Vec3[] = [];
        // 先计算所有饼的位置
        for (let colorInfo of colors) {
            let count = colorInfo.count;
            for (let i = 0; i < count; i++) {
                let x = this.savePanPos[index].x;
                let y = this.savePanPos[index].y + 10 + currentHeight * 20;
                currentHeight++;
                posArr.push(v3(x, y, 0));
            }
        }

        // 然后创建饼并设置正确的位置
        let bingIndex = 0; // 用于跟踪当前饼在posArr中的索引
        for (let colorInfo of colors) {
            let type = colorInfo.type;
            let count = colorInfo.count;
            for (let j = 0; j < count; j++) {
                let bing = Tools.newPrefab("Bing");
                bing.setParent(this.node);
                // 先全部放到初始位置（第一个位置）
                bing.setPosition(posArr[0]);
                Tools.setSpriteFrame(bing, "B" + type);
                bing.getComponent(BingTs).bingType = type;
                bing.active = false;
                bing.getComponent(BingTs).isCreate = true;
                bing.getComponent(UITransform).width = 180
                bing.getComponent(UITransform).height = 180
                arr.push(bing);
                bingIndex++; // 递增索引（虽然这里不直接使用，但保持一致性）
            }
        }
        // 只对顶部2个做动画，其他直接到位
        let goarr: Node[] = [];
        let total = arr.length;
        for (let m = arr.length - 1; m > -1; m--) {
            goarr.push(arr[m]);
            if (total - m <= 2) { // 只对顶部2个做动画
                this.scheduleOnce(() => {
                    arr[m].active = true;
                    for (let k = 0; k < goarr.length - 1; k++) {
                        tween(goarr[k])
                            .to(0.08, {
                                position: posArr[posArr.length - 1 - k]
                            })
                            .start();
                    }

                }, 0.05 * (total - m - 1));
            } else {
                arr[m].active = true;
                arr[m].setPosition(posArr[posArr.length - 1 - (goarr.length - 1)]);
                // arr[m].getComponent(BingTs).isCreate = false;
            }

        }
        setTimeout(() => {
            for (let i = 0; i < goarr.length; i++) {
                goarr[i].getComponent(BingTs).isCreate = false;
            }
        }, 500);

        // 层级设置逻辑不变
        if (arr.length <= this.disPlayIndex.length) {
            for (let i = 0; i < arr.length; i++) {
                arr[i].setSiblingIndex(this.disPlayIndex[i]);
            }
        } else {
            for (let i = 0; i < arr.length; i++) {
                if (i <= this.disPlayIndex.length - 1) {
                    arr[i].setSiblingIndex(this.disPlayIndex[i]);
                } else {
                    arr[i].setSiblingIndex(this.disPlayIndex[this.disPlayIndex.length - 1] + (i + 1 - this.disPlayIndex.length));
                }
            }
        }
        this.saveBing[index] = arr;
    }
    /**获取顾客最需要的饼类型*/
    getMostNeededBingType(): number {
        // 收集所有顾客的需求信息
        let allNeeds: { type: number, priority: number, urgency: number }[] = [];

        for (let peopleIndex = 0; peopleIndex < this.savePeople.length; peopleIndex++) {
            let people = this.savePeople[peopleIndex];
            if (!people) { continue; }

            let kuang = people.getComponent(peopleTs).kuang;
            if (kuang == null) { continue; }

            let kuangT = kuang.getComponent(kuangTs);
            let needBingType = kuangT.needBingType;
            let bingNum = kuangT.bingNum;

            // 安全检查：确保必要的数组存在
            if (!needBingType || !Array.isArray(needBingType) || !bingNum || !Array.isArray(bingNum)) {
                continue;
            }

            // 分析每个顾客的需求
            for (let needIndex = 0; needIndex < needBingType.length; needIndex++) {
                let needType = needBingType[needIndex];
                let needCount = bingNum[needIndex];

                // 跳过已完成的需求
                if (needCount <= 0 || needCount === -2) continue;

                // 计算优先级和紧急度
                let priority = needCount;  // 需求数量越多，优先级越高
                let urgency = 3 - peopleIndex;  // 顾客位置越靠前，紧急度越高

                allNeeds.push({ type: needType, priority, urgency });
            }
        }

        // 如果没有任何需求，返回-1表示没有需求
        if (allNeeds.length === 0) {
            return -1;
        }

        // 按紧急度和优先级排序
        allNeeds.sort((a, b) => {
            if (a.urgency !== b.urgency) {
                return b.urgency - a.urgency;  // 紧急度高的优先
            }
            return b.priority - a.priority;  // 需求数量多的优先
        });

        return allNeeds[0].type;
    }
    /**检查场上是否有顾客需要的饼类型（包括暂存区、盘子、提示摞）*/
    hasNeededBingOnField(neededBingType: number): boolean {
        // 检查所有盘子上的饼摞
        for (let i = 0; i < this.saveBing.length; i++) {
            if (this.saveBing[i] && this.saveBing[i].length > 0) {
                // 检查摞顶的饼
                let topBing = this.saveBing[i][this.saveBing[i].length - 1];
                if (topBing && topBing.getComponent(BingTs).bingType === neededBingType) {
                    // console.log(`盘子${i}有需要的饼类型${neededBingType}`);
                    return true;
                }
            }
        }

        // 检查所有提示摞
        for (let i = 0; i < this.saveTip.length; i++) {
            if (this.saveTip[i] && this.saveTip[i].length > 0) {
                // 检查摞顶的饼
                let topBing = this.saveTip[i][this.saveTip[i].length - 1];
                if (topBing && topBing.getComponent(BingTs).bingType === neededBingType) {
                    // console.log(`提示摞${i}有需要的饼类型${neededBingType}`);
                    return true;
                }
            }
        }
        // console.log(`场上没有找到需要的饼类型${neededBingType}`);
        return false;
    }
    /**在剩余配置中查找顶部饼类型匹配顾客需求的索引*/
    findMatchingConfigIndex(neededBingType: number): number {
        for (let x = 0; x < this.pileConfigurations.length; x++) {
            let config = this.pileConfigurations[x];
            if (config && config.colors && config.colors.length > 0) {
                // 检查这个配置的顶部饼类型（最后一个颜色）
                let topBingType = config.colors[config.colors.length - 1].type;
                if (topBingType === neededBingType) {
                    // console.log(`找到匹配配置: 索引${x}, 顶部饼类型${topBingType}`);
                    return x;
                }
            }
        }
        // console.log(`未找到匹配配置，需要的饼类型: ${neededBingType}`);
        return -1;
    }
    /**生成框 */
    CreteKuang(peopleNode: Node, pos: Vec3) {
        let kuang = Tools.newPrefab("kuang")
        kuang.setParent(this.node)
        kuang.setPosition(v3(pos.x, pos.y + 40, 0))
        peopleNode.getComponent(peopleTs).kuang = kuang
        kuang.getComponent(kuangTs).peopleNode = peopleNode
        // 依次分配订单
        let order = null;
        if (this.orders && this.orders.length > 0) {
            order = this.orders[0];
        }
        if (order) {
            let kuangT = kuang.getComponent(kuangTs);
            // 解析订单，设置 kuangTs 的属性
            console.log(this.orders[0], "订单");
            kuangT.needBingType = this.orders[0].piles.map(pile => pile.color);
            kuangT.bingNum = this.orders[0].piles.map(pile => pile.count);
            this.orders.splice(0, 1)
            // 初始化saveBing数组
            kuangT.saveBing = [];
            for (let i = 0; i < order.piles.length; i++) {
                kuangT.saveBing[i] = [];
            }
        }
    }
    /**大饼移动函数 */
    moveFunc(node: Node) {
        let index = 0;
        let find: boolean = false
        for (let i = 0; i < this.saveBing.length; i++) {
            if (this.saveBing[i] == null) { continue }
            for (let j = 0; j < this.saveBing[i].length; j++) {
                if (this.saveBing[i][j] == node) {
                    index = i;
                    find = true
                    this.disPlayIndex = []
                    for (let k = 0; k < this.saveBing[i].length; k++) {
                        let indexNum = this.saveBing[i][k].getSiblingIndex()
                        this.disPlayIndex.push(indexNum)
                    }
                    break;
                }
            }
            if (find) { break }
        }
        // console.log(find, "find")
        if (find) {
            // 找到空的暂存位置
            let emptyZancunIndex = -1;
            for (let i = 0; i < this.saveZancunBing.length; i++) {
                if (this.saveZancunBing[i].length === 0) {
                    emptyZancunIndex = i;
                    break;
                }
            }
            // 如果找到空的暂存位置，将整摞饼移动到暂存区
            if (emptyZancunIndex !== -1 && emptyZancunIndex < this.saveZancunPos.length && this.zanCunAnimationState[emptyZancunIndex] == 0) {
                let bings = this.saveBing[index];
                this.saveBing[index] = [];
                this.saveZancunBing[emptyZancunIndex] = bings;
                let zancunPos = this.saveZancunPos[emptyZancunIndex];
                this.zanCunAnimationState[emptyZancunIndex] = 1
                // 安全检查：确保 zancunPos 存在
                if (!zancunPos) {
                    // console.error(`暂存位置 ${emptyZancunIndex} 不存在，saveZancunPos 长度: ${this.saveZancunPos.length}`);
                    return;
                }
                node.getComponent(BingTs).isCreate = true
                for (let i = 0; i < bings.length; i++) {
                    let bing = bings[i];
                    // 设置饼的新位置
                    tween(bing)
                        .delay(0.005 * i)
                        .to(0.12, { position: new Vec3(zancunPos.x, zancunPos.y + 10 + i * 11, 0), scale: new Vec3(0.7, 0.7, 0.7) })
                        .start();
                    tween(bing)
                        .delay(0.12 + 0.005 * i)
                        .by(0.06, { scale: v3(0.1, -0.1, 0) })
                        .by(0.06, { scale: v3(-0.2, 0.2, 0) })
                        .by(0.06, { scale: v3(0.1, -0.1, 0) })
                        .start()
                    if (i == bings.length - 2) {
                        tween(bing)
                            .delay(0.12 + 0.005 * i)
                            .by(0.06, { position: v3(0, 15, 0) })
                            .by(0.06, { position: v3(0, -15, 0) })
                            .start()
                    }
                    if (i == bings.length - 1) {
                        tween(bing)
                            .delay(0.12 + 0.005 * i)
                            .by(0.06, { position: v3(0, 25, 0) })
                            .by(0.06, { position: v3(0, -25, 0) })
                            .start()
                    }
                    // 更新饼的状态
                }
                setTimeout(() => {
                    if (!this.hasEnd && !this.hasWon && !this.isDead) {
                        // console.log("饼移动完成，延迟检查交付，让玩家观察新状态");
                        // 🕐 额外延迟，让玩家看清新的游戏状态
                        setTimeout(() => {
                            if (!this.hasEnd && !this.hasWon && !this.isDead) {
                                this.checkGet();
                            }
                        }, 200); // 额外800ms延迟，让玩家观察

                    } else {
                        // console.log("饼移动完成时游戏已结束，跳过交付检查");
                    }

                }, 0.1 + 0.004 * (bings.length - 1)) // 适中的延迟，确保移动动画完成
                this.bingMoveAndTip(index)
            }

        }

        // 🕐 延迟重置触摸状态，确保动画完成后才能再次点击
        setTimeout(() => {
            this.isCanTouch = false;
            // console.log("触摸状态已重置，可以进行下次点击");
        }, this.touchResetDelay); // 使用配置的延迟时间
    }
    /**大饼中间的移动 */
    bingMoveAndTip(index) {
        let tipIndex = 0
        if (index == 0 || index == 4) {
            tipIndex = 0
        }
        if (index == 1 || index == 5) {
            tipIndex = 1
        }
        if (index == 2 || index == 6) {
            tipIndex = 2
        }
        if (index == 3 || index == 7) {
            tipIndex = 3
        }
        if (index <= 3) {
            let bings = this.saveBing[index + 4];
            if (bings.length != 0) {
                this.saveBing[index] = this.saveBing[index + 4]
                this.saveBing[index + 4] = []
                let currentHeight = 0
                let posArr: Vec3[] = []
                for (let k = 0; k < bings.length; k++) {
                    let x = this.savePanPos[index].x;
                    let y = this.savePanPos[index].y + 10 + currentHeight * 20;
                    currentHeight++;
                    posArr.push(v3(x, y, 0));
                }
                for (let i = 0; i < bings.length; i++) {
                    let bing = bings[i];
                    // 设置饼的新位置
                    tween(bing)
                        .delay(0.005 * i)
                        .to(0.12, { position: posArr[i] })
                        .call(() => { bing.getComponent(BingTs).isCreate = false })
                        .start();
                    tween(bing)
                        .delay(0.12 + 0.005 * i)
                        .by(0.06, { scale: v3(0.1, -0.1, 0) })
                        .by(0.06, { scale: v3(-0.2, 0.2, 0) })
                        .by(0.06, { scale: v3(0.1, -0.1, 0) })
                        .start()
                    if (i == bings.length - 2) {
                        tween(bing)
                            .delay(0.12 + 0.005 * i)
                            .by(0.06, { position: v3(0, 15, 0) })
                            .by(0.06, { position: v3(0, -15, 0) })
                            .start()
                    }
                    if (i == bings.length - 1) {
                        tween(bing)
                            .delay(0.12 + 0.005 * i)
                            .by(0.06, { position: v3(0, 25, 0) })
                            .by(0.06, { position: v3(0, -25, 0) })
                            .start()
                    }
                }
            }
        }
        // console.log(this.saveTip[tipIndex], "this.saveTip[tipIndex]", tipIndex, "tipIndex")
        if (this.saveTip[tipIndex] != null) {
            for (let children of this.saveTip[tipIndex]) {
                children.destroy()
            }
            this.saveTip[tipIndex] = null
            // console.log(this.saveTip)
        }
        if (this.saveTipFloor[tipIndex] != null) {
            // console.log("delete")
            this.saveTipFloor[tipIndex].destroy()
            this.saveTipFloor[tipIndex] = null
        }
        let currentHeight = 0
        let posArr: Vec3[] = []
        if (this.saveTipColors[tipIndex] == null) { return }
        let bings = this.createMoveBing(this.tipPos[tipIndex], this.saveTipColors[tipIndex].colors)
        this.saveTipColors[tipIndex] = null
        if (index <= 3) {
            this.saveBing[index + 4] = bings
        } else {
            this.saveBing[index] = bings
        }
        for (let k = 0; k < bings.length; k++) {
            let x = this.tipPos[tipIndex].x;
            let y = this.tipPos[tipIndex].y + 10 + currentHeight * 20;
            currentHeight++;
            posArr.push(v3(x, y, 0));
        }
        for (let i = 0; i < bings.length; i++) {
            let bing = bings[i];
            let pos = this.tipNodeMask.getComponent(UITransform).convertToNodeSpaceAR(
                this.node.getComponent(UITransform).convertToWorldSpaceAR(posArr[i])
            );
            // 设置饼的新位置
            tween(bing)
                .delay(0.005 * i)
                .to(0.12, { position: pos })
                .call(() => {
                    bing.getComponent(BingTs).isCreate = false
                    bing.setParent(this.node)
                    bing.setPosition(posArr[i])
                })
                .start();
            tween(bing)
                .delay(0.12 + 0.005 * i)
                .by(0.06, { scale: v3(0.1, -0.1, 0) })
                .by(0.06, { scale: v3(-0.2, 0.2, 0) })
                .by(0.06, { scale: v3(0.1, -0.1, 0) })
                .start()
            if (i == bings.length - 2) {
                tween(bing)
                    .delay(0.12 + 0.005 * i)
                    .by(0.06, { position: v3(0, 15, 0) })
                    .by(0.06, { position: v3(0, -15, 0) })
                    .start()
            }
            if (i == bings.length - 1) {
                tween(bing)
                    .delay(0.12 + 0.005 * i)
                    .by(0.06, { position: v3(0, 25, 0) })
                    .by(0.06, { position: v3(0, -25, 0) })
                    .start()
            }
        }
        // 智能选择下一个饼摞索引
        let nextBingIndex = 0
        // 获取顾客最需要的饼类型
        let mostNeededBingType = this.getMostNeededBingType();
        if (mostNeededBingType !== -1) {
            // 检查场上是否已经有顾客需要的饼类型
            let hasNeededBingOnField = this.hasNeededBingOnField(mostNeededBingType);
            if (!hasNeededBingOnField) {
                // 场上没有顾客需要的饼，检索剩余未生成的饼摞
                let matchingConfigIndex = this.findMatchingConfigIndex(mostNeededBingType);
                if (matchingConfigIndex !== -1) {
                    // 找到匹配的配置，将其索引设为nextBingIndex
                    nextBingIndex = matchingConfigIndex;
                }
            }
        }
        console.log(this.pileConfigurations.length, "this.pileConfigurations.length---点击之后的生成"
            , nextBingIndex, "nextBingIndex"
        );
        if (this.pileConfigurations[nextBingIndex]) {
            this.createTip(nextBingIndex, tipIndex, this.tipPos[tipIndex], this.pileConfigurations[nextBingIndex].colors)
        }
    }
    /**生成提示 */
    createTip(nextBingIndex: number, tipIndex: number, pos: Vec3, colors: { type: number, count: number }[]) {
        let arr: Node[] = [];
        let currentHeight = 0;
        let posArr: Vec3[] = [];
        let tip = Tools.newPrefab("tip")
        tip.setParent(this.node)
        tip.setPosition(v3(pos.x, pos.y - 250))
        tip.setSiblingIndex(4)
        // 先计算所有饼的位置
        for (let colorInfo of colors) {
            let count = colorInfo.count;
            for (let i = 0; i < count; i++) {
                let x = pos.x;
                let y = pos.y - 250 + currentHeight * 11;
                currentHeight++;
                posArr.push(v3(x, y, 0));
            }
        }
        // 然后创建饼并设置正确的位置
        let bingIndex = 0; // 用于跟踪当前饼在posArr中的索引
        for (let colorInfo of colors) {
            let type = colorInfo.type;
            let count = colorInfo.count;
            for (let j = 0; j < count; j++) {
                let bing = Tools.newPrefab("Bing");
                bing.setParent(this.node);
                bing.setPosition(posArr[bingIndex]); // 使用正确的索引
                Tools.setSpriteFrame(bing, "B" + type);
                bing.getComponent(BingTs).bingType = type;
                bing.getComponent(UITransform).width = 90
                bing.getComponent(UITransform).height = 90
                arr.push(bing);
                bingIndex++; // 递增索引
            }
        }

        // 层级设置逻辑不变
        if (arr.length <= this.disPlayIndex.length) {
            for (let i = 0; i < arr.length; i++) {
                arr[i].setSiblingIndex(this.disPlayIndex[i]);
            }
        } else {
            for (let i = 0; i < arr.length; i++) {
                if (i <= this.disPlayIndex.length - 1) {
                    arr[i].setSiblingIndex(this.disPlayIndex[i]);
                } else {
                    arr[i].setSiblingIndex(this.disPlayIndex[this.disPlayIndex.length - 1] + (i + 1 - this.disPlayIndex.length));
                }
            }
        }
        this.saveTipFloor[tipIndex] = tip
        this.saveTip[tipIndex] = arr
        this.saveTipColors[tipIndex] = { colors: colors }
        // console.log(isDaluan, "isDaluan")

        this.pileConfigurations.splice(nextBingIndex, 1);

    }
    /**生成要移动的饼 */
    createMoveBing(pos: Vec3, colors: { type: number, count: number }[]) {
        let arr: Node[] = [];
        let currentHeight = 0;
        let posArr: Vec3[] = [];
        // 先计算所有饼的位置
        for (let colorInfo of colors) {
            let count = colorInfo.count;
            for (let i = 0; i < count; i++) {
                let x = pos.x;
                let y = pos.y - 400 + 10 + currentHeight * 20;
                currentHeight++;
                posArr.push(v3(x, y, 0));
            }
        }

        // 然后创建饼并设置正确的位置
        let bingIndex = 0; // 用于跟踪当前饼在posArr中的索引
        for (let colorInfo of colors) {
            let type = colorInfo.type;
            let count = colorInfo.count;
            for (let j = 0; j < count; j++) {
                let bing = Tools.newPrefab("Bing");
                bing.setParent(this.tipNodeMask);
                // 使用正确的索引获取位置
                let worldPos = this.node.getComponent(UITransform).convertToWorldSpaceAR(posArr[bingIndex]);
                let localPos = this.tipNodeMask.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                bing.setPosition(v3(localPos.x, localPos.y));
                Tools.setSpriteFrame(bing, "B" + type);
                bing.getComponent(BingTs).bingType = type;
                bing.getComponent(BingTs).isCreate = true;
                bing.getComponent(UITransform).width = 180
                bing.getComponent(UITransform).height = 180
                arr.push(bing);
                bingIndex++; // 递增索引
            }
        }
        // 层级设置逻辑不变
        if (arr.length <= this.disPlayIndex.length) {
            for (let i = 0; i < arr.length; i++) {
                arr[i].setSiblingIndex(this.disPlayIndex[i]);
            }
        } else {
            for (let i = 0; i < arr.length; i++) {
                if (i <= this.disPlayIndex.length - 1) {
                    arr[i].setSiblingIndex(this.disPlayIndex[i]);
                } else {
                    arr[i].setSiblingIndex(this.disPlayIndex[this.disPlayIndex.length - 1] + (i + 1 - this.disPlayIndex.length));
                }
            }
        }
        // 从配置数组中移除已使用的配置
        return arr
    }
    /**检查是否可以交付 */
    checkGet() {
        // 前置状态检查
        if (this.hasEnd || this.hasWon || this.isDead) {
            // console.log("游戏已结束，跳过交付检查");
            return;
        }
        if (this.isMove) {
            // console.log("正在移动中，跳过交付检查");
            return;
        }
        // console.log("开始交付检查...");

        // 🔄 重置交付检测完成状态
        this.isAllDeliveryCheckCompleted = false;
        // this.lastDeliveryCheckResult = "检测进行中...";

        let hasDelivery: boolean = false;
        let totalDeliveryAttempts = 0;
        // 按顺序遍历所有暂存区中的每一摞饼
        for (let i = 0; i < this.saveZancunBing.length; i++) {
            // 跳过空的暂存区
            if (this.saveZancunBing[i].length === 0) {
                continue;
            }
            // 对当前摞饼从最上层开始向下检查交付
            // 从摞顶开始逐个检查每个饼
            for (let j = this.saveZancunBing[i].length - 1; j >= 0; j--) {
                let currentBing = this.saveZancunBing[i][j];
                let currentBingType = currentBing.getComponent(BingTs).bingType;
                // 检查是否有顾客需要这种类型的饼
                let targetCustomerIndex = -1;
                let targetNeedIndex = -1;
                // 收集所有匹配的顾客及其完成度
                let matchingCustomers: {
                    peopleIndex: number,
                    needIndex: number,
                    completionRate: number,
                    remainingTotal: number
                }[] = [];
                for (let peopleIndex = 0; peopleIndex < this.savePeople.length; peopleIndex++) {
                    let people = this.savePeople[peopleIndex];
                    if (!people) { continue; }
                    let kuang = people.getComponent(peopleTs).kuang;
                    if (kuang == null) { continue; }
                    let kuangT = kuang.getComponent(kuangTs);
                    let needBingType = kuangT.needBingType;
                    let bingNum = kuangT.bingNum;
                    // 安全检查：确保必要的数组存在
                    if (!needBingType || !Array.isArray(needBingType) || !bingNum || !Array.isArray(bingNum)) {
                        // console.warn("顾客数据不完整，跳过该顾客", peopleIndex);
                        continue;
                    }
                    // 检查该顾客是否需要当前类型的饼
                    for (let k = 0; k < needBingType.length; k++) {
                        if (needBingType[k] === currentBingType && bingNum[k] > 0 && bingNum[k] !== -2) {
                            // 计算该顾客的完成度
                            let totalNeeded = 0;
                            let totalRemaining = 0;
                            for (let m = 0; m < bingNum.length; m++) {
                                if (bingNum[m] !== -2) {
                                    // 安全检查：确保 saveBing[m] 存在
                                    if (kuangT.saveBing && kuangT.saveBing[m] && Array.isArray(kuangT.saveBing[m])) {
                                        totalNeeded += kuangT.saveBing[m].length;
                                    }
                                    totalRemaining += bingNum[m];
                                }
                            }
                            let completionRate = totalNeeded > 0 ? (totalNeeded - totalRemaining) / totalNeeded : 0;
                            matchingCustomers.push({
                                peopleIndex: peopleIndex,
                                needIndex: k,
                                completionRate: completionRate,
                                remainingTotal: totalRemaining
                            });
                            break; // 找到匹配后跳出内层循环
                        }
                    }
                }
                // 🎯 优先选择能够完全满足的订单
                if (matchingCustomers.length > 0) {
                    // 首先检查是否有顾客的这个需求可以被完全满足
                    let canBeFullySatisfied = [];
                    let partialSatisfied = [];

                    for (let customer of matchingCustomers) {
                        let people = this.savePeople[customer.peopleIndex];
                        let kuang = people.getComponent(peopleTs).kuang;
                        let kuangT = kuang.getComponent(kuangTs);
                        let bingNum = kuangT.bingNum;

                        // 检查当前摞中这种类型的饼数量是否足够满足该顾客的需求
                        let availableCount = 0;
                        for (let k = j; k >= 0; k--) {
                            let bing = this.saveZancunBing[i][k];
                            if (bing.getComponent(BingTs).bingType === currentBingType) {
                                availableCount++;
                            } else {
                                break; // 遇到不同类型的饼就停止计数
                            }
                        }

                        if (availableCount >= bingNum[customer.needIndex]) {
                            // 可以完全满足这个需求
                            canBeFullySatisfied.push(customer);
                        } else {
                            // 只能部分满足
                            partialSatisfied.push(customer);
                        }
                    }

                    let selectedCustomer;
                    if (canBeFullySatisfied.length > 0) {
                        // 优先选择能完全满足的，按完成度排序
                        canBeFullySatisfied.sort((a, b) => {
                            if (Math.abs(a.completionRate - b.completionRate) < 0.01) {
                                return a.remainingTotal - b.remainingTotal;
                            }
                            return b.completionRate - a.completionRate;
                        });
                        selectedCustomer = canBeFullySatisfied[0];
                        // console.log(`🎯 选择能完全满足的顾客 ${selectedCustomer.peopleIndex}，需求 ${selectedCustomer.needIndex}`);
                    } else {
                        // 没有能完全满足的，选择部分满足中完成度最高的
                        partialSatisfied.sort((a, b) => {
                            if (Math.abs(a.completionRate - b.completionRate) < 0.01) {
                                return a.remainingTotal - b.remainingTotal;
                            }
                            return b.completionRate - a.completionRate;
                        });
                        selectedCustomer = partialSatisfied[0];
                        // console.log(`📋 选择部分满足的顾客 ${selectedCustomer.peopleIndex}，需求 ${selectedCustomer.needIndex}`);
                    }

                    targetCustomerIndex = selectedCustomer.peopleIndex;
                    targetNeedIndex = selectedCustomer.needIndex;
                }
                // 如果找到匹配的顾客，收集可交付的饼
                if (targetCustomerIndex !== -1) {
                    // 收集从当前位置开始的所有匹配饼
                    let deliveryBatch = this.collectMatchingBings(i, j, targetCustomerIndex, targetNeedIndex);
                    if (deliveryBatch.length > 0) {
                        this.deliverBatchToPeople(deliveryBatch, targetCustomerIndex, targetNeedIndex);
                        hasDelivery = true;
                        totalDeliveryAttempts++;
                        // 批量交付后延迟继续检查，确保动画完成 - 给足够时间让玩家看清交付效果
                        setTimeout(() => {
                            if (!this.hasEnd && !this.hasWon && !this.isDead) {
                                // console.log(`交付完成，继续检查（第${totalDeliveryAttempts}次交付）`)
                                this.checkGet();
                            } else {
                                // console.log("游戏已结束，停止交付检查");
                            }
                        }, 200); // 调整延迟，确保动画效果可见
                        return;
                    }
                } else {
                    // 遇到不匹配顾客需求的饼时立即停止交付该摞
                    break;
                }
            }
        }
        // 如果没有任何交付发生，进行失败检测
        if (!hasDelivery) {
            this.isMove = false;
            // console.log(`交付检查完成 - 无可交付饼，总尝试次数: ${totalDeliveryAttempts}`);

            // 🛡️ 检查是否有交付动画正在进行中
            if (this.globalDeliveryCounter > 0) {
                // console.log(`检测到 ${this.globalDeliveryCounter} 个交付动画正在进行，延迟失败检测`);
                // 延迟失败检测，等待交付动画完成
                setTimeout(() => {
                    if (!this.hasEnd && !this.hasWon && !this.isDead) {
                        // console.log("交付动画等待完成，重新检查失败条件");
                        // 重置计数器并重新检查
                        this.globalDeliveryCounter = 0;
                        this.requestDeadCheck();
                    }
                }, 1000); // 给足够时间让交付动画完成

                // ✅ 设置检测完成状态（延迟失败检测情况）
                this.isAllDeliveryCheckCompleted = true;
                this.lastDeliveryCheckResult = "延迟失败检测 - 等待交付动画完成";
                this.deliveryCheckCompletedTime = Date.now();
                // console.log("📋 交付检测已完成（延迟失败检测）");
                return; // 暂时不进行失败检测
            }

            // 重置全局交付计数器，为下次交付做准备
            this.globalDeliveryCounter = 0;
            // 检查游戏是否已经结束，避免在结束后继续检查
            if (this.hasEnd || this.hasWon || this.isDead || this.isMove) {
                return;
            }
            // 延迟执行失败检测，确保所有可能的交付都已检查完毕
            // console.log("准备执行失败检测，延迟确保完整性...");
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    // console.log("延迟失败检测开始");
                    // 🛡️ 在失败检测前，再次检查是否有交付正在进行
                    if (this.globalDeliveryCounter > 0) {
                        // console.log("检测到交付动画正在进行中，再次延迟失败检测");
                        setTimeout(() => {
                            if (!this.hasEnd && !this.hasWon && !this.isDead) {
                                this.requestDeadCheck();
                            }
                        }, 1000); // 额外延迟等待交付完成
                    } else {
                        this.requestDeadCheck();
                    }
                } else {
                    // console.log("延迟期间游戏已结束，取消失败检测");
                }
            }, 1000); // 给足够时间让交付动画完成
            // ✅ 设置检测完成状态（无交付情况）
            this.isAllDeliveryCheckCompleted = true;
            // this.lastDeliveryCheckResult = `检测完成 - 无可交付饼，总尝试次数: ${totalDeliveryAttempts}`;
            this.deliveryCheckCompletedTime = Date.now();
            // console.log("📋 交付检测已完成（无交付）");
            return false
        } else {
            // console.log(`交付检查完成 - 发现可交付饼，总交付次数: ${totalDeliveryAttempts}`);
            // ✅ 设置检测完成状态（有交付情况）
            this.isAllDeliveryCheckCompleted = true;
            // this.lastDeliveryCheckResult = `检测完成 - 发现可交付饼，总交付次数: ${totalDeliveryAttempts}`;
            this.deliveryCheckCompletedTime = Date.now();
            // console.log("📋 交付检测已完成（有交付）");
        }
    }
    /**获取交付检测完成状态*/
    getDeliveryCheckCompletionStatus(): {
        isCompleted: boolean,
        result: string,
        completedTime: number,
        timeSinceCompletion: number
    } {
        let timeSinceCompletion = this.isAllDeliveryCheckCompleted ?
            Date.now() - this.deliveryCheckCompletedTime : 0;

        return {
            isCompleted: this.isAllDeliveryCheckCompleted,
            result: this.lastDeliveryCheckResult,
            completedTime: this.deliveryCheckCompletedTime,
            timeSinceCompletion: timeSinceCompletion
        };
    }
    /**检查交付检测是否已完成*/
    isDeliveryCheckCompleted(): boolean {
        return this.isAllDeliveryCheckCompleted;
    }
    /**获取最后一次检测结果*/
    getLastDeliveryCheckResult(): string {
        return this.lastDeliveryCheckResult;
    }
    /**收集可批量交付的饼*/
    collectMatchingBings(zancunIndex: number, startIndex: number, peopleIndex: number, needIndex: number): {
        bing: Node,
        zancunIndex: number,
        bingIndex: number
    }[] {
        let people = this.savePeople[peopleIndex];
        if (!people) { return []; }

        let kuang = people.getComponent(peopleTs).kuang;
        if (kuang == null) { return []; }

        let kuangT = kuang.getComponent(kuangTs);
        let bingNum = kuangT.bingNum;
        let needBingType = kuangT.needBingType;

        // 安全检查：确保必要的数组存在
        if (!needBingType || !Array.isArray(needBingType) || !bingNum || !Array.isArray(bingNum)) {
            // console.warn("顾客数据不完整，无法收集饼", peopleIndex);
            return [];
        }

        if (needIndex >= needBingType.length || needIndex >= bingNum.length) {
            // console.warn("needIndex 超出范围", needIndex, needBingType.length, bingNum.length);
            return [];
        }

        let targetBingType = needBingType[needIndex];

        let batch: { bing: Node, zancunIndex: number, bingIndex: number }[] = [];
        let remainingNeed = bingNum[needIndex];

        // 从指定位置开始向下收集匹配的饼
        for (let j = startIndex; j >= 0 && remainingNeed > 0; j--) {
            let currentBing = this.saveZancunBing[zancunIndex][j];
            if (!currentBing || !currentBing.isValid) continue;

            let currentBingType = currentBing.getComponent(BingTs).bingType;
            if (currentBingType === targetBingType) {
                batch.push({
                    bing: currentBing,
                    zancunIndex: zancunIndex,
                    bingIndex: j
                });
                remainingNeed--;
            } else {
                // 遇到不匹配的饼就停止
                break;
            }
        }

        return batch;
    }
    /**批量交付饼给指定顾客*/
    deliverBatchToPeople(batch: { bing: Node, zancunIndex: number, bingIndex: number }[], peopleIndex: number, needIndex: number) {
        if (batch.length === 0) return;
        let people = this.savePeople[peopleIndex];
        let kuang = people.getComponent(peopleTs).kuang;
        if (kuang == null) return;
        let kuangT = kuang.getComponent(kuangTs);
        let bingNum = kuangT.bingNum;
        // 1. 先处理数据：从暂存区移除所有饼并更新需求数量
        let validBings: { bing: Node, targetBingIndex: number }[] = [];
        for (let i = 0; i < batch.length; i++) {
            let item = batch[i];
            let bing = item.bing;
            if (!bing || !bing.isValid) continue;

            // 安全检查：确保saveBing数组存在且有效
            if (!kuangT.saveBing || !kuangT.saveBing[needIndex] || !Array.isArray(kuangT.saveBing[needIndex])) {
                // console.warn(`saveBing数组无效，needIndex: ${needIndex}, saveBing:`, kuangT.saveBing);
                continue;
            }

            // 计算目标饼的索引（在更新需求数量之前）
            let targetBingIndex = bingNum[needIndex] - 1;
            let targetBing = kuangT.saveBing[needIndex][targetBingIndex];
            if (!targetBing) continue;
            // 从暂存区移除饼
            let bingIndexInStack = this.saveZancunBing[item.zancunIndex].indexOf(bing);
            if (bingIndexInStack != -1) {
                this.saveZancunBing[item.zancunIndex].splice(bingIndexInStack, 1);
                if (this.saveZancunBing[item.zancunIndex].length == 0) {
                    setTimeout(() => {
                        this.zanCunAnimationState[item.zancunIndex] = 0
                    }, 400);
                }
            }
            // 更新需求数量
            bingNum[needIndex]--;
            this.bingNum--;
            validBings.push({ bing: bing, targetBingIndex: targetBingIndex });
        }
        // 2. 执行动画
        this.executeBatchDeliveryAnimation(validBings, kuang, peopleIndex, needIndex);
        // 3. 重新整理暂存区位置
        for (let item of batch) {
            this.resetStackPositions(item.zancunIndex);
        }
    }
    /**执行批量交付动画*/
    executeBatchDeliveryAnimation(validBings: { bing: Node, targetBingIndex: number }[], kuang: Node, peopleIndex: number, needIndex: number) {
        if (validBings.length === 0) return;
        let kuangT = kuang.getComponent(kuangTs);
        let bingNum = kuangT.bingNum;
        let completedCount = 0;
        // 获取全局交付计数器，确保所有交付都有唯一的延迟
        if (!this.globalDeliveryCounter) {
            this.globalDeliveryCounter = 0;
        }
        // 为每个饼执行动画
        for (let i = 0; i < validBings.length; i++) {
            let item = validBings[i];
            let bing = item.bing;

            // 安全检查：确保saveBing数组存在且有效
            if (!kuangT.saveBing || !kuangT.saveBing[needIndex] || !Array.isArray(kuangT.saveBing[needIndex])) {
                // console.warn(`executeBatchDeliveryAnimation: saveBing数组无效，needIndex: ${needIndex}, saveBing:`, kuangT.saveBing);
                // 如果没有目标饼，直接销毁
                this.removeBingFromAllArrays(bing);
                bing.destroy();
                completedCount++;
                continue;
            }

            let targetBing = kuangT.saveBing[needIndex][item.targetBingIndex];
            if (!targetBing) {
                // 如果没有目标饼，直接销毁
                this.removeBingFromAllArrays(bing);
                bing.destroy();
                completedCount++;
                continue;
            }
            // 计算目标位置
            let worldPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(
                targetBing.parent.getComponent(UITransform).convertToWorldSpaceAR(targetBing.position)
            );

            // 使用全局计数器确保所有交付都有合适的间隔
            let globalDelay = 0.12 * this.globalDeliveryCounter; // 增加间隔，从0.08改为0.15
            this.globalDeliveryCounter++;

            // 执行移动动画 - 放慢交付速度，让玩家看清过程
            tween(bing)
                .delay(globalDelay) // 使用全局延迟，确保所有交付都有序进行
                .call(() => {
                    bing.setSiblingIndex(9999)
                })
                .to(0.25, { // 增加动画时间，从0.25改为0.4，让交付过程更清晰
                    position: worldPos,
                    scale: new Vec3(0.35, 0.35, 0.35)
                })
                .call(() => {
                    bing.setSiblingIndex(9999)
                    // 销毁交付的饼
                    this.removeBingFromAllArrays(bing);
                    bing.destroy();
                    AudioTools._ins.playAudio("xiaochu");
                    completedCount++;
                    // 🔄 交付动画完成，减少全局计数器
                    this.globalDeliveryCounter = Math.max(0, this.globalDeliveryCounter - 1);
                    // console.log(`交付动画完成，剩余动画数量: ${this.globalDeliveryCounter}`);

                    // 当所有饼都完成动画时
                    if (completedCount === validBings.length) {
                        // 检查该类型需求是否完全满足
                        if (bingNum[needIndex] === 0) {
                            // 🛡️ 数据验证：确保索引有效
                            if (needIndex >= 0 && needIndex < bingNum.length) {
                                console.log(`✅ 标记需求 ${needIndex} 为已完成，原值: ${bingNum[needIndex]}`);
                                bingNum[needIndex] = -2; // 标记为已完成
                                console.log(`✅ 标记完成后，bingNum[${needIndex}] = ${bingNum[needIndex]}`);
                            } else {

                            }
                            // 只在需求完全满足时生成对号
                            let pos2 = v3(worldPos.x, worldPos.y - 100, 0);
                            let getNode = this.createGet(pos2, kuang);
                            // 延迟删除对号 - 给玩家更多时间看到成功反馈
                            setTimeout(() => {
                                if (getNode && getNode.isValid) {
                                    // 播放对号消失动画 - 放慢消失速度
                                    tween(getNode)
                                        .to(0.3, { scale: v3(0, 0, 0) }) // 增加动画时间，从0.2改为0.3
                                        .call(() => {
                                            getNode.destroy();
                                            // 从 kuangT.get 数组中移除
                                            let getIndex = kuangT.get.indexOf(getNode);
                                            if (getIndex !== -1) {
                                                kuangT.get.splice(getIndex, 1);
                                            }
                                        })
                                        .start();
                                }
                            }, 600); // 增加显示时间，从400改为600ms，让玩家充分看到成功反馈
                        }
                        // 检查顾客是否完全满足
                        this.checkCustomerCompletion(peopleIndex);
                    }
                })
                .start();

            // 目标饼的消失动画 - 放慢消失速度，让效果更明显
            tween(targetBing)
                .delay(globalDelay) // 与移动动画同步，使用相同的全局延迟
                .by(0.25, { // 增加动画时间，从0.18改为0.25，让消失效果更明显
                    scale: new Vec3(0.1, 0.1, 0.1)
                })
                .by(0.15, { // 增加动画时间，从0.12改为0.15
                    scale: new Vec3(-0.1, -0.1, -0.1)
                })
                .call(() => {
                    // 销毁顾客框中的目标饼
                    targetBing.destroy();
                    // 从顾客需求数组中移除已销毁的饼
                    kuangT.saveBing[needIndex][item.targetBingIndex] = null;
                })
                .start();

            tween(targetBing.getComponent(UIOpacity))
                .delay(0.15) // 增加延迟，从0.12改为0.15
                .by(0.25, { // 增加动画时间，从0.18改为0.25，保证视觉效果
                    opacity: 150
                })
                .by(0.15, { // 增加动画时间，从0.12改为0.15
                    opacity: 0
                })
                .start();
        }
    }
    /**检查顾客是否完全满足*/
    checkCustomerCompletion(peopleIndex: number) {
        let people = this.savePeople[peopleIndex];
        if (!people) { return; }

        let kuang = people.getComponent(peopleTs).kuang;
        if (kuang == null) { return; }

        let kuangT = kuang.getComponent(kuangTs);

        // 检查是否所有需求都已满足
        let allCompleted = true;
        let completedCount = 0;
        for (let i = 0; i < kuangT.bingNum.length; i++) {
            if (kuangT.bingNum[i] === -2) {
                completedCount++;
            } else {
                allCompleted = false;
            }
        }

        // 如果所有需求都已满足，完成订单
        // 修复：只需要检查所有需求类型是否都完成，不需要检查对号数量
        if (allCompleted) {
            if (this.peopleNum > 3) {
                this.isHavePeopleleave = true;
            }
            this.completeOrder(peopleIndex, 0); // zancunIndex 在这里不重要
        }
    }
    /**重新整理指定暂存区的饼位置*/
    resetStackPositions(zancunIndex: number) {
        if (!this.saveZancunBing[zancunIndex]) { return; }
        // 安全检查：确保暂存位置存在
        if (zancunIndex >= this.saveZancunPos.length || !this.saveZancunPos[zancunIndex]) {
            // console.error(`暂存位置 ${zancunIndex} 不存在，saveZancunPos 长度: ${this.saveZancunPos.length}`);
            return;
        }
        let stack = this.saveZancunBing[zancunIndex];
        for (let j = 0; j < stack.length; j++) {
            let bing = stack[j];
            if (!bing || !bing.isValid) continue;
            bing.setPosition(
                this.saveZancunPos[zancunIndex].x,
                this.saveZancunPos[zancunIndex].y + 10 + j * 11,
                0
            );
        }
    }
    /**彻底移除所有数组中的饼引用*/
    removeBingFromAllArrays(bing: Node) {
        // 移除所有 saveZancunBing
        for (let i = 0; i < this.saveZancunBing.length; i++) {
            this.saveZancunBing[i] = this.saveZancunBing[i].filter(b => b !== bing);
        }
        // 移除所有 saveBing
        for (let i = 0; i < this.saveBing.length; i++) {
            if (this.saveBing[i]) {
                this.saveBing[i] = this.saveBing[i].filter(b => b !== bing);
            }
        }
    }
    /**移出已满足的需求 */
    removeCompletedNeed(peopleIndex: number, zancunIndex: number) {
        let people = this.savePeople[peopleIndex];
        if (!people) { return }
        let kuang = people.getComponent(peopleTs).kuang;
        if (kuang == null) {
            return;
        }

        let kuangT = kuang.getComponent(kuangTs);
        // 检查是否所有需求都已满足
        let allSatisfied = true;
        for (let i = 0; i < kuangT.bingNum.length; i++) {
            if (kuangT.bingNum[i] != -2) {
                allSatisfied = false;
                break;
            }
        }

        if (allSatisfied) {
            if (this.peopleNum > 3) {
                this.isHavePeopleleave = true
            }
            this.completeOrder(peopleIndex, zancunIndex);
        }

    }
    /**顾客全部满足 */
    completeOrder(peopleIndex: number, zancunIndex: number) {
        let people = this.savePeople[peopleIndex];
        people.getComponent(peopleTs).checkGuke = false;
        let kuang = people.getComponent(peopleTs).kuang;
        this.starFeedback(kuang.position)

        // 销毁框
        tween(kuang)
            .delay(0.2)
            .call(() => {
                for (let child of kuang.getComponent(kuangTs).get) {
                    child.destroy()
                }
            })
            .to(0.2, { scale: v3(0, 0, 0) })
            .call(() => {
                kuang.destroy();
                people.getComponent(peopleTs).kuang = null;
                // 顾客满意动画
                tween(people)
                    .to(0.2, { scale: new Vec3(1.2, 1.2, 1.2) })
                    .to(0.2, { scale: new Vec3(0, 0, 0) })
                    .delay(0.1) // 等待一段时间后离开
                    .call(() => {
                        // 销毁人物
                        // 从数组中移除人物
                        let index = this.savePeople.indexOf(people);
                        if (index !== -1) {
                            this.savePeople[index] = null
                        }
                        // console.log("要被销毁的顾客", people)
                        people.destroy();
                        // 检查是否还有未上场的订单
                        if (this.orders.length > 0) {
                            // 安全检查：确保索引在有效范围内
                            if (index >= 0 && index < this.savePeoplePos.length) {
                                // 创建新的人物
                                this.createPeople(this.savePeoplePos[index], index)
                            } else {
                                // console.error(`创建人物时索引超出范围，index: ${index}, savePeoplePos 长度: ${this.savePeoplePos.length}`);
                            }
                            // this.createNewPeople(index);
                        }
                        // console.log(this.bingNum, "剩余饼的数量");
                        if (this.bingNum == 0) {
                            this.createWin()
                            // console.log("游戏胜利");
                        }
                    })
                    .start();
            })
            .start();
    }
    /**生成获取的对勾 */
    createGet(pos: Vec3, node: Node): Node {
        let get = Tools.newPrefab("get")
        get.setParent(this.node)
        get.setPosition(pos)
        get.scale = v3(0, 0, 0)
        tween(get)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .delay(0.1)
            .to(0.1, { scale: v3(1, 1, 1) })
            .start(
        )
        node.getComponent(kuangTs).get.push(get)
        return get; // 返回创建的对号节点
    }
    /**星星反馈 */
    starFeedback(pos: Vec3) {
        for (let i = 0; i < 10; i++) {
            let newkuai = Tools.newPrefab("star")//cc.instantiate(kuai);
            if (Math.random() > 0.5) {
                let number = Math.random() * 0.2 + 0.5;
                newkuai.scale = v3(number, number, number)
            } else {
                let number = Math.random() * 0.5 + 0.5;
                newkuai.scale = v3(number, number, number)
            }
            newkuai.setParent(this.node)
            newkuai.setPosition(v3(pos.x, pos.y + 80))
            let angle = Math.random() * 360;
            let vec = v3(Math.cos(angle * Math.PI / 180), Math.sin(angle * Math.PI / 180), 0);
            this.scheduleOnce(() => {
                tween(newkuai)
                    .by(0.15, { position: vec.multiplyScalar(Tools.random(10, 15, true)) })
                    .start()
                tween(newkuai)
                    .by(Math.random() * 0.3 + 0.1, { position: vec.multiplyScalar(Tools.random(7, 13, true)) })
                    .start()
                tween(newkuai)
                    .by(Math.random() * 0.4 + 0.3, { angle: Math.random() * 360 - 180 })
                    .start()
                tween(newkuai)
                    .delay(0.1)
                    .by(0.3, { position: v3(0, -15, 0) })
                    .call(() => {
                        newkuai.destroy()
                    })
                    .start()
            }, 0.05 + Math.random() * 0.1)
        }
    }
    /**新订单创建完成回调*/
    onNewOrderCreated() {
        // console.log("新订单创建完成，准备检查交付");
        // 检查游戏状态
        if (this.hasEnd || this.hasWon || this.isDead) {
            // console.log("游戏已结束，跳过新订单交付检查");
            return;
        }
        // 确保订单完全显示后再检查交付 - 减少延迟，加快游戏节奏
        setTimeout(() => {
            if (!this.hasEnd && !this.hasWon && !this.isDead) {
                // console.log("新订单展示完成，开始交付检查");
                this.checkGet();
            } else {
                // console.log("新订单展示期间游戏结束，取消交付检查");
            }
        }, 200); // 减少观察时间，从300改为200ms
    }
    /**检查是否死关了 */
    checkDead() {
        // console.log("开始死关检测");
        // 首先检查是否有已完成的订单等待顾客离开
        if (this.hasCompletedOrdersWaitingToLeave()) {
            // console.log("发现已完成的订单等待顾客离开，跳过死关检测");
            return;
        }
        let can: boolean = false
        let begin = 0
        for (let m = 0; m < this.saveZancunBing.length; m++) {
            if (this.saveZancunBing[m].length != 0) {
                begin++
                // break
            }
        }
        if (begin == 0) {
            return
        }
        if (begin == 5) {
            // 使用新的交付逻辑检查是否还能继续游戏
            for (let i = 0; i < this.saveZancunBing.length; i++) {
                // 跳过空的暂存区
                if (this.saveZancunBing[i].length === 0) {
                    continue;
                }
                // 从摞顶开始逐个检查每个饼（与新交付逻辑一致）
                for (let j = this.saveZancunBing[i].length - 1; j >= 0; j--) {
                    let currentBing = this.saveZancunBing[i][j];
                    if (!currentBing || !currentBing.isValid) continue;
                    let currentBingType = currentBing.getComponent(BingTs).bingType;
                    // 检查是否有顾客需要这种类型的饼
                    let canDeliver = this.checkCanDeliverWithNewLogic(i, j, currentBingType);
                    if (canDeliver) {
                        can = true;
                        break;
                    } else {
                        // 遇到不匹配的饼时停止检查该摞（与新交付逻辑一致）
                        break;
                    }
                }
                if (can) {
                    break;
                }
            }
            if (can == false) {
                // 检查是否是因为暂存区满了而无法交付的情况
                let isStorageFullButCanDeliver = this.checkIfStorageFullButCanDeliver();
                if (!isStorageFullButCanDeliver) {
                    // 🔒 首先检查订单创建锁定状态
                    if (this.isOrderCreationLocked || this.ordersInCreation > 0) {
                        // console.log(`🔒 订单创建中，跳过死关检测 - 创建中订单数: ${this.ordersInCreation}`);
                        return; // 订单创建期间不进行死关检测
                    }

                    // 🚶 检查是否有顾客正在离开过程中或即将创建新顾客
                    if (this.peopleNum > 0 && this.orders.length > 0) {
                        // 检查是否有空的顾客位置（说明有顾客刚离开或即将创建新顾客）
                        let emptyPositions = 0;
                        for (let i = 0; i < this.savePeople.length; i++) {
                            if (this.savePeople[i] == null) {
                                emptyPositions++;
                            }
                        }

                        if (emptyPositions > 0) {
                            // console.log(`🚶 检测到${emptyPositions}个空位置，可能有顾客正在离开或新顾客即将创建，跳过死关检测`);
                            return; // 顾客状态变化期间不进行死关检测
                        }
                    }
                    // 检查是否所有顾客都在场
                    let allCustomersPresent = this.areAllCustomersPresent();
                    // 只有在所有顾客都在场且都无法满足交付条件时才触发失败
                    if (allCustomersPresent && this.savePeople.some(people => people != null) && !this.isMove && !this.hasWon && this.bingNum != 0 && !this.isHavePeopleleave && !this.hasEnd) {
                        // console.log("触发游戏失败：暂存区满，所有顾客都在场，且都无法满足交付条件");
                        if (this.isDead) { return }
                        this.isDead = true
                        this.scheduleOnce(() => {
                            this.gameEnd()
                        }, 1)
                    } else {
                        // console.log("未满足失败条件：", {
                        //     allCustomersPresent,
                        //     hasActivePeople: this.savePeople.some(people => people != null),
                        //     isMove: this.isMove,
                        //     hasWon: this.hasWon,
                        //     bingNum: this.bingNum,
                        //     isHavePeopleleave: this.isHavePeopleleave,
                        //     hasEnd: this.hasEnd,
                        //     订单创建锁定: this.isOrderCreationLocked,
                        //     创建中订单数: this.ordersInCreation
                        // });
                    }
                }
            }
        }
    }
    /**检查是否有已完成的订单等待顾客离开*/
    hasCompletedOrdersWaitingToLeave(): boolean {
        // console.log("检查已完成订单状态...");
        // 遍历 savePeople 数组中的每个顾客位置（索引 0, 1, 2）
        for (let i = 0; i < this.savePeople.length; i++) {
            let people = this.savePeople[i];
            // 检查是否有顾客存在
            if (!people) {
                // console.log(`位置 ${i}: 无顾客`);
                continue;
            }
            let kuang = people.getComponent(peopleTs).kuang;
            if (!kuang) {
                // console.log(`位置 ${i}: 顾客存在但无需求框`);
                continue;
            }
            let kuangT = kuang.getComponent(kuangTs);
            if (!kuangT || !kuangT.bingNum || !Array.isArray(kuangT.bingNum)) {
                // console.log(`位置 ${i}: 需求框数据无效`);
                continue;
            }
            // 检查该顾客的订单是否已经完成（所有需求都满足，即 bingNum 中所有值都为 -2）
            let isOrderCompleted = true;
            let completedCount = 0;
            for (let j = 0; j < kuangT.bingNum.length; j++) {
                if (kuangT.bingNum[j] === -2) {
                    completedCount++;
                } else {
                    isOrderCompleted = false;
                }
            }
            // 如果发现有任何一个订单已经完成，同时还有未分配的新订单
            if (isOrderCompleted && this.orders.length > 0) {
                // console.log(`发现已完成的订单在位置 ${i}，还有未分配订单 ${this.orders.length - this.orderIndex} 个`);
                return true;
            }
        }
        // console.log("没有发现已完成的订单等待离开");
        return false;
    }
    /**请求死关检查（精确状态版本）*/
    requestDeadCheck() {
        let checkGukeNum = 0
        for (let i = 0; i < 3; i++) {
            if (this.savePeople[i] == null) {
                checkGukeNum++
                // console.log(i, "空位置")
                continue
            }
            if (this.savePeople[i].getComponent(peopleTs).checkGuke) {
                // console.log(i, "不空位置")
                checkGukeNum++
            }
        }
        // console.log(checkGukeNum, "checkGukeNum");
        if (checkGukeNum != 3) { return }
        if (this.isMove) {
            // console.log("正在移动中，跳过交付检查");
            return;
        }
        // console.log("=== 失败检测请求开始 ===");

        // 🔒 订单创建锁定状态检查
        if (this.isOrderCreationLocked || this.ordersInCreation > 0) {
            // console.log(`🔒 订单创建锁定中，暂停死关检测 - 创建中订单数: ${this.ordersInCreation}`);
            // 延迟重新检查，等待订单创建完成
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    this.requestDeadCheck();
                }
            }, 1000); // 短暂延迟后重新检查
            return;
        }
        // 🚶 检查是否有顾客正在离开过程中或即将创建新顾客
        if (this.peopleNum > 0 && this.orders.length > 0) {
            // 检查是否有空的顾客位置（说明有顾客刚离开或即将创建新顾客）
            let emptyPositions = 0;
            for (let i = 0; i < this.savePeople.length; i++) {
                if (this.savePeople[i] == null) {
                    emptyPositions++;
                }
            }

            if (emptyPositions > 0) {
                // console.log(`🚶 检测到${emptyPositions}个空位置，可能有顾客正在离开或新顾客即将创建，延迟死关检测`);
                setTimeout(() => {
                    if (!this.hasEnd && !this.hasWon && !this.isDead) {
                        // console.log("顾客状态变化等待结束，重新检查失败条件");
                        this.requestDeadCheck();
                    }
                }, 1000); // 给足够时间让顾客离开和新顾客创建
                return;
            }
        }

        // 最终状态检查
        if (this.hasEnd || this.hasWon || this.isDead) {
            // console.log("游戏已结束，取消失败检测");
            return;
        }

        // 第一层检查：是否有已完成的订单等待顾客离开
        if (this.hasCompletedOrdersWaitingToLeave()) {
            // console.log("发现已完成订单等待顾客离开，延迟失败检测");
            // 延迟重新检查，等待顾客离开
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    // console.log("顾客离开等待结束，重新检查失败条件");
                    this.requestDeadCheck();
                }
            }, 1000);
            return;
        }
        // 第二层检查：分析游戏状态决定检测策略
        let hasActiveCustomers = this.savePeople.some(people => people != null);
        let hasUnusedOrders = this.orders.length > 0;
        let canCreateNewPeople = this.peopleNum > 0;

        if (!hasActiveCustomers && hasUnusedOrders && canCreateNewPeople) {
            // 场景1：没有活跃顾客但还有未使用订单且能创建新顾客
            // console.log("检测到新订单创建条件，延迟失败检测等待新顾客");
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    // console.log("新顾客创建等待结束，重新评估失败条件");
                    this.requestDeadCheck();
                }
            }, 1000); // 给足够时间让新顾客创建
        } else if (hasActiveCustomers || (!hasUnusedOrders && !canCreateNewPeople)) {
            // 场景2：有活跃顾客，或者没有更多订单且无法创建新顾客

            // 🚶 在执行死关检测前，再次检查是否有空位置且能创建新顾客
            if (hasUnusedOrders && canCreateNewPeople) {
                let emptyPositions = 0;
                for (let i = 0; i < this.savePeople.length; i++) {
                    if (this.savePeople[i] == null) {
                        emptyPositions++;
                    }
                }

                if (emptyPositions > 0) {
                    //      console.log(`🚶 场景2中检测到${emptyPositions}个空位置，延迟死关检测等待新顾客创建`);
                    setTimeout(() => {
                        if (!this.hasEnd && !this.hasWon && !this.isDead) {
                            // console.log("场景2空位置等待结束，重新检查失败条件");
                            this.requestDeadCheck();
                        }
                    }, 1000);
                    return;
                }
            }

            // console.log("满足失败检测条件，执行最终检测");
            // 再次延迟确保所有异步操作完成
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    // console.log("=== 开始最终失败检测 ===");
                    this.checkDead();
                } else {
                    // console.log("最终检测前游戏已结束");
                }
            }, 100);
        } else {
            // 场景3：其他边界情况
            // console.log("游戏状态不明确，延迟重新评估");
            setTimeout(() => {
                if (!this.hasEnd && !this.hasWon && !this.isDead) {
                    this.requestDeadCheck();
                }
            }, 1000);
        }
    }
    /**检查是否可以使用新交付逻辑进行交付*/
    checkCanDeliverWithNewLogic(zancunIndex: number, bingIndex: number, bingType: number): boolean {
        // 收集所有匹配的顾客及其完成度（与checkGet中的逻辑一致）
        let matchingCustomers: {
            peopleIndex: number,
            needIndex: number,
            completionRate: number,
            remainingTotal: number
        }[] = [];
        for (let peopleIndex = 0; peopleIndex < this.savePeople.length; peopleIndex++) {
            let people = this.savePeople[peopleIndex];
            if (!people) { continue; }
            let kuang = people.getComponent(peopleTs).kuang;
            if (kuang == null) { continue; }
            let kuangT = kuang.getComponent(kuangTs);
            let needBingType = kuangT.needBingType;
            let bingNum = kuangT.bingNum;
            // 安全检查：确保必要的数组存在
            if (!needBingType || !Array.isArray(needBingType) || !bingNum || !Array.isArray(bingNum)) {
                // console.warn("顾客数据不完整，跳过该顾客", peopleIndex);
                continue;
            }
            // 检查该顾客是否需要当前类型的饼
            for (let k = 0; k < needBingType.length; k++) {
                if (needBingType[k] === bingType && bingNum[k] > 0 && bingNum[k] !== -2) {
                    // 计算该顾客的完成度
                    let totalNeeded = 0;
                    let totalRemaining = 0;
                    for (let m = 0; m < bingNum.length; m++) {
                        if (bingNum[m] !== -2) {
                            // 安全检查：确保 saveBing[m] 存在
                            if (kuangT.saveBing && kuangT.saveBing[m] && Array.isArray(kuangT.saveBing[m])) {
                                totalNeeded += kuangT.saveBing[m].length;
                            }
                            totalRemaining += bingNum[m];
                        }
                    }
                    let completionRate = totalNeeded > 0 ? (totalNeeded - totalRemaining) / totalNeeded : 0;

                    matchingCustomers.push({
                        peopleIndex: peopleIndex,
                        needIndex: k,
                        completionRate: completionRate,
                        remainingTotal: totalRemaining
                    });
                    break;
                }
            }
        }
        if (matchingCustomers.length === 0) {
            return false;
        }
        // 按完成度排序（与checkGet中的逻辑一致）
        matchingCustomers.sort((a, b) => {
            if (Math.abs(a.completionRate - b.completionRate) < 0.01) {
                return a.remainingTotal - b.remainingTotal;
            }
            return b.completionRate - a.completionRate;
        });
        // 选择优先级最高的顾客
        let targetCustomer = matchingCustomers[0];
        // 检查是否可以收集到至少1个饼进行交付
        let deliveryBatch = this.collectMatchingBings(zancunIndex, bingIndex, targetCustomer.peopleIndex, targetCustomer.needIndex);

        return deliveryBatch.length > 0;
    }
    /**检查是否是暂存区满了但还能交付的情况*/
    checkIfStorageFullButCanDeliver(): boolean {
        // 首先检查暂存区是否真的满了
        let isStorageFull = true;
        for (let i = 0; i < this.saveZancunBing.length; i++) {
            if (this.saveZancunBing[i].length === 0) {
                isStorageFull = false;
                break;
            }
        }
        // 如果暂存区没满，不是这种情况
        if (!isStorageFull) {
            return false;
        }
        // 暂存区满了，检查是否还有可以交付的饼
        // 这里需要检查是否有正在进行的交付或即将进行的交付
        // 方法1：检查是否有交付动画正在进行
        if (this.isMove) {
            return true; // 有交付正在进行，等待完成
        }
        // 方法2：检查暂存区顶部是否有可以立即交付的饼
        for (let i = 0; i < this.saveZancunBing.length; i++) {
            if (this.saveZancunBing[i].length === 0) continue;
            // 检查摞顶的饼
            let topBingIndex = this.saveZancunBing[i].length - 1;
            let topBing = this.saveZancunBing[i][topBingIndex];
            if (!topBing || !topBing.isValid) continue;
            let topBingType = topBing.getComponent(BingTs).bingType;
            // 使用简化的检查：是否有顾客需要这种类型的饼
            for (let peopleIndex = 0; peopleIndex < this.savePeople.length; peopleIndex++) {
                let people = this.savePeople[peopleIndex];
                if (!people) continue;
                let kuang = people.getComponent(peopleTs).kuang;
                if (kuang == null) continue;
                let kuangT = kuang.getComponent(kuangTs);
                let needBingType = kuangT.needBingType;
                let bingNum = kuangT.bingNum;

                // 安全检查：确保必要的数组存在
                if (!needBingType || !Array.isArray(needBingType) || !bingNum || !Array.isArray(bingNum)) {
                    // console.warn("顾客数据不完整，跳过该顾客", peopleIndex);
                    continue;
                }
                for (let k = 0; k < needBingType.length; k++) {
                    if (needBingType[k] === topBingType && bingNum[k] > 0 && bingNum[k] !== -2) {
                        // 找到了可以交付的饼
                        return true;
                    }
                }
            }
        }

        // 暂存区满了且没有可以交付的饼
        return false;
    }
    /**检查是否所有顾客都在场 */
    areAllCustomersPresent(): boolean {
        // 检查是否3个顾客位置都被占用且有完整的需求框
        let actualCustomerCount = 0;
        for (let i = 0; i < this.savePeople.length; i++) {
            let people = this.savePeople[i];
            if (people != null) {
                // 检查顾客是否有完整的需求框
                let peopleComponent = people.getComponent(peopleTs);
                if (peopleComponent && peopleComponent.kuang != null) {
                    actualCustomerCount++;
                }
            }
        }
        // 所有顾客都在场意味着3个位置都被占用
        let allPresent = actualCustomerCount == 3;
        // console.log(`顾客在场检查 - 实际: ${actualCustomerCount}/3, 所有在场: ${allPresent}`);
        return allPresent;
    }
    /**游戏结束 */
    gameEnd() {
        this.hasEnd = true
        for (let i = 0; i < this.saveZancunBing.length; i++) {
            if (this.saveZancunBing[i].length != 0) {
                for (let j = 0; j < this.saveZancunBing[i].length; j++) {
                    let bing = this.saveZancunBing[i][j]
                    tween(bing)
                        .delay(0.2)
                        .call(() => {
                            bing.getComponent(Sprite).color = new Color(255, 0, 0, 255)
                        })
                        .delay(0.2)
                        .call(() => {
                            bing.getComponent(Sprite).color = new Color(255, 255, 255, 255)
                        })
                        .delay(0.2)
                        .call(() => {
                            bing.getComponent(Sprite).color = new Color(255, 0, 0, 255)
                        })
                        .delay(0.2)
                        .call(() => {
                            bing.getComponent(Sprite).color = new Color(255, 255, 255, 255)
                        })
                        .start()
                }
            }
        }
        setTimeout(() => {
            GameModel._ins.mainGame.gameEnd()
        }, 1000);
    }
    /**生成胜利界面 */
    createWin() {
        if (this.hasWon) { return }
        this.hasWon = true
        let win = Tools.newPrefab("win")
        win.setParent(this.node)
        win.setPosition(0, 0)
        win.active = false
        this.scheduleOnce(() => {
            win.active = true
        }, 0.5)
    }
    isLoopTouch: boolean = false

    /********************************************************************************** */
    /**重置菜单 */
    resetCaiDan() {
        if (this.isLoopTouch || this.isMove || this.isDead || this.hasWon) { return }
        this.isLoopTouch = true
        tween(this.auto)
            .to(0.2, { scale: v3(1.2, 1.2, 1) })
            .to(0.2, { scale: v3(1, 1, 1) })
            .call(() => {
                this.resetOrder()
            })
            .start()
    }
    /**重置菜单逻辑 */
    resetOrder() {
        // 1. 获取场上所有people节点及其kuang脚本
        let peopleNodes: Node[] = [];
        for (let i = 0; i < this.savePeople.length; i++) {
            if (this.savePeople[i]) {
                peopleNodes.push(this.savePeople[i]);
            }
        }
        // 3. 统计未交付的订单（即场上people的kuang
        let unDeliveredOrders: any[] = [];
        for (let children of peopleNodes) {
            let kuang = children.getComponent(peopleTs).kuang
            if (kuang.getComponent(kuangTs) && kuang.getComponent(kuangTs).needBingType && kuang.getComponent(kuangTs).bingNum) {
                unDeliveredOrders.push({
                    types: kuang.getComponent(kuangTs).needBingType,
                    nums: kuang.getComponent(kuangTs).bingNum
                });
            }
        }
        let customerNeedCounts = new Array(this.bingColor).fill(0);
        for (let i = 0; i < this.savePeople.length; i++) {
            if (this.savePeople[i]) {
                let kuang = this.savePeople[i].getComponent(peopleTs).kuang;
                if (kuang && kuang.getComponent(kuangTs)) {
                    let kuangT = kuang.getComponent(kuangTs);
                    let needBingType = kuangT.needBingType;
                    let bingNum = kuangT.bingNum;
                    if (needBingType && bingNum) {
                        for (let j = 0; j < needBingType.length; j++) {
                            let type = needBingType[j];
                            let num = bingNum[j];
                            if (num > 0 && num !== -2) {
                                if (type >= 0 && type < this.bingColor) {
                                    customerNeedCounts[type] += num;
                                }
                            }
                        }
                    }
                }
            }
        }
        console.log("顾客未完成需求各颜色饼的数量:", customerNeedCounts);
        // 统计resetOrders中的饼的种类和数量
        let resetOrdersColorCounts = new Array(this.bingColor).fill(0);
        let totalBings = 0;
        if (this.orders && Array.isArray(this.orders)) {
            for (let order of this.orders) {
                if (order && order.piles && Array.isArray(order.piles)) {
                    for (let pile of order.piles) {
                        if (pile && typeof pile.color === 'number' && typeof pile.count === 'number' &&
                            pile.color >= 0 && pile.color < this.bingColor) {
                            resetOrdersColorCounts[pile.color] += pile.count;
                            totalBings += pile.count;
                        }
                    }
                }
            }
        }
        // 5.3 统计所有颜色的总需求（暂存区 + 顾客需求 + 未生成订单）
        let totalColorCounts = new Array(this.bingColor).fill(0);
        // 添加顾客需求数量
        for (let i = 0; i < this.bingColor; i++) {
            totalColorCounts[i] += customerNeedCounts[i];
        }
        // 添加未生成订单数量
        for (let i = 0; i < this.bingColor; i++) {
            totalColorCounts[i] += resetOrdersColorCounts[i];
        }

        // 重新构建的订单分配算法
        let newOrders: { piles: { color: number, count: number }[] }[] = [];
        let remainingCounts = [...totalColorCounts]; // 复制数组，避免修改原数组

        // 持续分配直到所有饼都被分配完
        while (remainingCounts.some(count => count > 0)) {
            let orderPiles: { color: number, count: number }[] = [];
            // 每个订单1-2摞
            let pilesInOrder = Math.floor(Math.random() * 2) + 1;
            // console.log(`创建订单，包含${pilesInOrder}摞`);
            for (let pileIndex = 0; pileIndex < pilesInOrder; pileIndex++) {
                // 找到所有还有剩余的颜色
                let availableColors = remainingCounts
                    .map((count, index) => ({ count, index }))
                    .filter(item => item.count > 0);
                if (availableColors.length === 0) {
                    // console.log("没有可用颜色，跳出摞循环");
                    break;
                }

                // 随机选择一个颜色
                let randomColorInfo = availableColors[Math.floor(Math.random() * availableColors.length)];
                let selectedColor = randomColorInfo.index;
                let availableCount = randomColorInfo.count;

                // 根据剩余数量确定这摞的饼数
                let pileCount;
                if (availableCount >= 7) {
                    // 大于等于7个：随机3-7个
                    pileCount = Math.floor(Math.random() * 5) + 3; // 3-7
                    // console.log(`颜色${selectedColor}有${availableCount}个(>=7)，分配${pileCount}个`);
                } else if (availableCount >= 3) {
                    // 3-6个：随机3到最大数量
                    pileCount = Math.floor(Math.random() * (availableCount - 3 + 1)) + 3; // 3到availableCount
                    // console.log(`颜色${selectedColor}有${availableCount}个(3-6)，分配${pileCount}个`);
                } else {
                    // 少于3个：全部分配
                    pileCount = availableCount;
                    // console.log(`颜色${selectedColor}有${availableCount}个(<3)，全部分配${pileCount}个`);
                }

                // 添加到当前订单
                orderPiles.push({
                    color: selectedColor,
                    count: pileCount
                });

                // 更新剩余数量
                remainingCounts[selectedColor] -= pileCount;
                // console.log(`颜色${selectedColor}分配${pileCount}个后，剩余${remainingCounts[selectedColor]}个`);
            }

            // 如果这个订单有内容，添加到订单列表
            if (orderPiles.length > 0) {
                newOrders.push({ piles: orderPiles });
                // console.log(`订单${newOrders.length}创建完成:`, orderPiles);
            }

            // 安全检查：防止无限循环
            if (newOrders.length > 50) {
                // console.error("订单数量过多，强制退出");
                break;
            }
        }

        // 验证分配结果
        let verifyColorCounts = new Array(this.bingColor).fill(0);
        let verifyTotal = 0;
        for (let order of newOrders) {
            for (let pile of order.piles) {
                verifyColorCounts[pile.color] += pile.count;
                verifyTotal += pile.count;
            }
        }
        // 检查是否完全匹配
        let isValid = true;
        for (let i = 0; i < this.bingColor; i++) {
            if (verifyColorCounts[i] !== totalColorCounts[i]) {
                isValid = false;
            }
        }
        if (isValid) {
            console.log("✅ 分配验证通过，数量完全匹配");
        } else {
            console.error("❌ 分配验证失败，数量不匹配");
        }
        console.log(newOrders, "newOrders")
        // 8. 更新orders数组
        this.orders = newOrders;
        this.peopleNum = newOrders.length;
        console.log("订单已更新，新订单数量:", this.peopleNum);
        // 8. 删除场上所有顾客的现有订单并重新生成
        for (let i = 0; i < this.savePeople.length; i++) {
            if (this.savePeople[i] != null) {
                tween(this.savePeople[i].getComponent(peopleTs).kuang)
                    .to(0.2, { scale: v3(0, 0, 1) })
                    .call(() => {
                        this.savePeople[i].getComponent(peopleTs).kuang.destroy()
                        if (i == this.savePeople.length - 1) {
                            for (let j = 0; j < this.savePeople.length; j++) {
                                if (this.savePeople[j] != null) {
                                    let pos = v3(this.savePeople[j].position.x, this.savePeople[j].position.y + this.savePeople[j].getComponent(UITransform).height / 1.5);
                                    GameModel._ins.bgTs.CreteKuang(this.savePeople[j], pos);
                                }
                            }
                            this.isLoopTouch = false
                        }
                    })
                    .start()
                this.peopleNum--
            }
        }

    }
    /********************************************************************************** */
    /**重置逻辑 */
    resetTianPin() {
        if (this.isLoopTouch || this.isMove || this.isDead || this.hasWon) { return }
        this.isLoopTouch = true
        tween(this.chongZhi)
            .to(0.2, { scale: v3(1.2, 1.2, 1) })
            .to(0.2, { scale: v3(1, 1, 1) })
            .call(() => {
                this.resetBing()
            })
            .start()
    }
    /**重置甜品的逻辑 */
    resetBing() {
        // 1. 收集场上所有饼节点
        let allBings: Node[] = [];
        let allTips: Node[] = [];
        // 收集主游戏区的饼
        for (let i = 0; i < this.saveBing.length; i++) {
            if (this.saveBing[i]) {
                allBings = allBings.concat(this.saveBing[i]);
            }
        }
        // 收集提示区的饼
        for (let i = 0; i < this.saveTip.length; i++) {
            if (this.saveTip[i]) {
                allTips = allTips.concat(this.saveTip[i]);
            }
        }
        // 2. 统计所有饼的颜色数量（不分主游戏区和提示区）
        let totalColorCounts = new Array(this.bingColor).fill(0);
        // 统计场上所有饼
        for (let bing of allBings) {
            if (bing && bing.isValid) {
                let bingType = bing.getComponent(BingTs).bingType;
                if (bingType >= 0 && bingType < this.bingColor) {
                    totalColorCounts[bingType]++;
                }
            }
        }
        for (let tip of allTips) {
            if (tip && tip.isValid) {
                let tipType = tip.getComponent(BingTs).bingType;
                if (tipType >= 0 && tipType < this.bingColor) {
                    totalColorCounts[tipType]++;
                }
            }
        }
        // 3. 收集剩余未生成的配置
        let remainingConfigs = [...this.pileConfigurations];
        // 4. 将剩余配置中的饼也统计到总数中
        for (let config of remainingConfigs) {
            for (let colorInfo of config.colors) {
                let type = colorInfo.type;
                let count = colorInfo.count;
                if (type >= 0 && type < this.bingColor) {
                    totalColorCounts[type] += count;
                }
            }
        }
        console.log("所有饼的颜色统计:", totalColorCounts);
        // 5. 使用饼消失动画，完成后再生成新饼
        for (let i = 0; i < allBings.length; i++) {
            allBings[i].destroy()
        }
        for (let i = 0; i < allTips.length; i++) {
            allTips[i].destroy()
        }
        for (let i = 0; i < this.saveTipFloor.length; i++) {
            if (this.saveTipFloor[i] != null) {
                this.saveTipFloor[i].destroy()
            }
        }
        // 清空引用
        for (let i = 0; i < this.saveBing.length; i++) {
            this.saveBing[i] = [];
        }
        for (let i = 0; i < this.saveTip.length; i++) {
            this.saveTip[i] = [];
        }
        for (let i = 0; i < this.saveTipColors.length; i++) {
            this.saveTipColors[i] = null
        }
        this.saveTipFloor = []
        // 重新生成配置，就像游戏重新开始一样
        this.pileConfigurations = this.generateResetConfigurations(totalColorCounts);
        // 重新生成主游戏区饼
        let panCount = Math.min(this.savePan.length, this.pileConfigurations.length);
        for (let i = 0; i < panCount; i++) {
            this.scheduleOnce(() => {
                if (this.pileConfigurations.length > 0) {
                    this.createBing(i, this.pileConfigurations[0].colors);
                    // 设置初始状态：立即设置为不可见，避免闪现
                    this.pileConfigurations.splice(0, 1);
                }
            }, 0.1 * i)
        }
        // 重新生成提示区饼
        this.scheduleOnce(() => {
            console.log(this.pileConfigurations.length, "this.pileConfigurations.length---重置之后的生成")
            let tipCount = Math.min(4, this.pileConfigurations.length);
            for (let i = 0; i < tipCount; i++) {
                this.scheduleOnce(() => {
                    if (this.pileConfigurations.length > 0) {
                        this.createTip(0, i, this.tipPos[i], this.pileConfigurations[0].colors)
                        console.log(this.pileConfigurations.length, "this.pileConfigurations.length---重置生成之后")
                    }
                }, 0.1 * i)
            }
            console.log("重置饼操作完成");
            this.isLoopTouch = false;
        }, 0.1 * panCount)

    }
    /**为重置功能生成配置 - 简单直接地重新分配所有饼 */
    generateResetConfigurations(colorCounts: number[]): { colors: { type: number, count: number }[] }[] {
        let configurations: { colors: { type: number, count: number }[] }[] = [];
        let remainingCounts = [...colorCounts]; // 复制数组
        // 如果没有饼，直接返回空配置
        if (remainingCounts.every(count => count === 0)) {
            return configurations;
        }
        // 计算总饼数
        let totalBings = remainingCounts.reduce((sum, count) => sum + count, 0);
        console.log("打乱前总饼数:", totalBings);
        // 收集顾客需求信息
        let customerNeeds: number[] = [];
        for (let i = 0; i < this.savePeople.length; i++) {
            let people = this.savePeople[i];
            if (people && people.getComponent(peopleTs).kuang) {
                let kuang = people.getComponent(peopleTs).kuang;
                let kuangT = kuang.getComponent(kuangTs);
                let needBingType = kuangT.needBingType;
                let bingNum = kuangT.bingNum;
                // 收集未完成的需求类型
                for (let j = 0; j < needBingType.length; j++) {
                    if (bingNum[j] > 0 && bingNum[j] !== -2) {
                        customerNeeds.push(needBingType[j]);
                    }
                }
            }
        }
        console.log("顾客需求类型:", customerNeeds);
        while (remainingCounts.some(count => count > 0)) {
            let pileColors: { type: number, count: number }[] = [];
            let totalInPile = 0;

            // 按剩余数量排序，但优先考虑顾客需求
            let colorIndices = remainingCounts.map((count, index) => ({ count, index }))
                .filter(item => item.count > 0)
                .sort((a, b) => {
                    // 优先级：顾客需要的颜色 > 数量多的颜色
                    let aIsNeeded = customerNeeds.indexOf(a.index) !== -1;
                    let bIsNeeded = customerNeeds.indexOf(b.index) !== -1;
                    if (aIsNeeded && !bIsNeeded) return -1;
                    if (!aIsNeeded && bIsNeeded) return 1;
                    // 都需要或都不需要时，按数量排序
                    return b.count - a.count;
                });

            // 计算这个堆的大小
            let remainingTotal = remainingCounts.reduce((sum, count) => sum + count, 0);
            let pileSize = Math.min(
                Math.floor(Math.random() * 5) + 3, // 3-7个饼
                remainingTotal
            );

            // 随机选择1-3种颜色混合，但优先包含顾客需要的颜色
            let colorTypeCount = Math.min(
                Math.floor(Math.random() * 3) + 1,
                colorIndices.length
            );

            // 如果第一个颜色是顾客需要的，增加选择更多颜色的概率
            if (colorIndices.length > 0 && customerNeeds.indexOf(colorIndices[0].index) !== -1) {
                colorTypeCount = Math.min(
                    Math.max(2, Math.floor(Math.random() * 3) + 1), // 至少选2种颜色
                    colorIndices.length
                );
            }
            // 如果这是最后一堆，确保使用所有剩余的饼
            let isLastPile = remainingTotal <= 7;
            if (isLastPile) {
                pileSize = remainingTotal;
                colorTypeCount = colorIndices.length;
            }
            for (let i = 0; i < colorTypeCount && totalInPile < pileSize; i++) {
                let colorIndex = colorIndices[i].index;
                let maxForThisColor = Math.min(
                    remainingCounts[colorIndex],
                    pileSize - totalInPile
                );

                let countForThisColor;
                if (isLastPile) {
                    // 最后一堆，使用所有剩余的饼
                    countForThisColor = remainingCounts[colorIndex];
                } else if (i === 0) {
                    // 主色占大部分，如果是顾客需要的颜色，稍微增加比例
                    let mainColorRatio = customerNeeds.indexOf(colorIndex) !== -1 ? 0.75 : 0.7;
                    countForThisColor = Math.max(1, Math.min(
                        Math.ceil(pileSize * mainColorRatio),
                        maxForThisColor
                    ));
                } else {
                    // 其他颜色平分剩余
                    let remaining = pileSize - totalInPile;
                    let remainingColors = colorTypeCount - i;
                    countForThisColor = Math.max(1, Math.min(
                        Math.ceil(remaining / remainingColors),
                        maxForThisColor
                    ));
                }

                pileColors.push({
                    type: colorIndex,
                    count: countForThisColor
                });
                remainingCounts[colorIndex] -= countForThisColor;
                totalInPile += countForThisColor;
            }

            if (pileColors.length > 0) {
                configurations.push({ colors: pileColors });
            }
        }
        // 随机打乱配置顺序，但优先把包含顾客需求的配置放在前面
        configurations.sort((a, b) => {
            let aHasNeeded = a.colors.some(color => customerNeeds.indexOf(color.type) !== -1);
            let bHasNeeded = b.colors.some(color => customerNeeds.indexOf(color.type) !== -1);
            if (aHasNeeded && !bHasNeeded) return -1;
            if (!aHasNeeded && bHasNeeded) return 1;
            // 都包含或都不包含时随机排序
            return Math.random() - 0.5;
        });

        // 验证生成的饼总数
        let generatedTotal = 0;
        for (let config of configurations) {
            for (let color of config.colors) {
                generatedTotal += color.count;
            }
        }
        console.log("打乱后总饼数:", generatedTotal);
        console.log("饼数是否一致:", totalBings === generatedTotal);
        return configurations;
    }
}
