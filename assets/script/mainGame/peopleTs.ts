import { _decorator, Component, game, Node, sp, tween, UITransform, v3 } from 'cc';
import { GameModel } from '../model/GameModel';
import { Tools } from '../common/Tools';
const { ccclass, property } = _decorator;

@ccclass('peopleTs')
export class peopleTs extends Component {
    /** 人物名字 */
    nameArray: string[] = [];
    /**人物身体 */
    @property(Node)
    body: Node = null
    /**人的框 */
    kuang: Node = null
    @property(sp.SkeletonData)
    sketonData: sp.SkeletonData = null
    peoplename: string = null
    /**是否接受检查 */
    checkGuke: boolean = false
    start() {
        // 收集已存在的顾客名称
        let otherName: string[] = []
        for (let i = 0; i < 3; i++) {
            if (GameModel._ins.bgTs.savePeople[i] != null) {
                let existingPeople = GameModel._ins.bgTs.savePeople[i].getComponent(peopleTs);
                if (existingPeople && existingPeople.peoplename) {
                    otherName.push(existingPeople.peoplename);
                }
            }
        }
        // 生成唯一的顾客名称
        let num: number;
        let name: string;
        do {
            num = Tools.random(1, 9, true);
            name = "P" + num;
        } while (otherName.indexOf(name) !== -1);

        // 设置顾客外观
        if (num == 1 || num == 8 || num == 9) {
            this.node.children[0].setPosition(v3(0, -70, 0));
        }
        // 确保 name 不为空再设置精灵帧
        this.peoplename = name;
        if (name && name !== "undefined") {
            Tools.setSpriteFrame(this.node.children[0], name);
        }
        // 顾客出现动画
        this.node.scale = v3(1, 0, 1);
        tween(this.node)
            .to(0.2, { scale: v3(1, 1, 1) })
            .call(() => {
                let pos = v3(this.node.position.x, this.node.position.y + this.node.getComponent(UITransform).height / 1.5);
                GameModel._ins.bgTs.CreteKuang(this.node, pos);
            })
            .start();
    }
    update(deltaTime: number) {

    }
}

