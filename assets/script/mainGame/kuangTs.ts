import { _decorator, Component, Label, Node, UITransform, v3, Vec3 } from 'cc';
import { GameModel } from '../model/GameModel';
import { Tools } from '../common/Tools';
import { peopleTs } from './peopleTs';
const { ccclass, property } = _decorator;

@ccclass('kuangTs')
export class kuangTs extends Component {
    /**需要的饼的种类 */
    needBingType: number[] = [];
    /**存储所需要的饼的位置 */
    saveNeedBingPos: Vec3[] = [];
    /**每一摞饼的数量 */
    bingNum: number[] = [];
    /**存储饼 */
    saveBing: Node[][] = [];
    get: Node[] = [];

    peopleNode: Node = null
    start() {
        this.cretateNeedBingPos()
        this.createNeedBing(this.node, this.needBingType, this.saveNeedBingPos, this.bingNum)

    }
    update(deltaTime: number) {

    }
    /**计算可以再对话框生成饼的位置 */
    cretateNeedBingPos() {
        if (this.needBingType.length == 1) {
            let x = 0;
            let y = this.node.getComponent(UITransform).height / 3 + 15
            this.saveNeedBingPos.push(v3(x, y, 0))
            this.node.getComponent(UITransform).width = 248
            Tools.setSpriteFrame(this.node, "duihuakuang2")
        } else {
            for (let i = 0; i < this.needBingType.length; i++) {
                let x = -80 + 115 * i + 50 * i
                let y = this.node.getComponent(UITransform).height / 3 + 15;
                this.saveNeedBingPos.push(v3(x, y, 0))
            }
        }
    }
    /**生成框内需要的饼 */
    createNeedBing(kuangNode: Node, bingTypeArr: number[], posARR: Vec3[], bingNum: number[]) {
        for (let i = 0; i < bingTypeArr.length; i++) {
            this.saveBing[i] = []
            for (let j = 0; j < bingNum[i]; j++) {
                let bing = Tools.newPrefab("Bing")
                bing.setParent(kuangNode)
                bing.setPosition(posARR[i].x, posARR[i].y + 10 + 14 * j, 0)
                bing.scale = v3(0.7, 0.7, 0.7)
                Tools.setSpriteFrame(bing, "B" + bingTypeArr[i])
                this.saveBing[i].push(bing)
            }
            if (i == bingTypeArr.length - 1) {
                console.log("新订单已生成，解锁订单创建状态")
                // 🔓 解锁订单创建状态
                GameModel._ins.bgTs.ordersInCreation--;
                if (GameModel._ins.bgTs.ordersInCreation <= 0) {
                    GameModel._ins.bgTs.ordersInCreation = 0;
                    GameModel._ins.bgTs.isOrderCreationLocked = false;
                    console.log("🔓 所有订单创建完成，解锁订单创建状态");
                }
                // 通知新订单创建完成
                GameModel._ins.bgTs.isHavePeopleleave = false
                // 延迟一段时间让玩家看到订单，然后再检查交付
                // 检查游戏状态，避免在游戏结束后继续检查交付
                if (!GameModel._ins.bgTs.hasEnd && !GameModel._ins.bgTs.hasWon && !GameModel._ins.bgTs.isDead) {
                    // 🕐 延迟交付检查，让玩家充分观察新订单1
                    if (!GameModel._ins.bgTs.hasEnd && !GameModel._ins.bgTs.hasWon && !GameModel._ins.bgTs.isDead) {
                        console.log("新订单展示完成，开始检查交付");
                        GameModel._ins.bgTs.checkGet();
                    } else {
                        console.log("新订单展示期间游戏结束，取消交付检查");
                    }
                    this.peopleNode.getComponent(peopleTs).checkGuke = true
                }
            }

        }
    }
}

