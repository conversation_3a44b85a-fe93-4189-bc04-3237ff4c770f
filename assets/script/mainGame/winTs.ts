import { _decorator, Component, Game, game, Label, Node, tween, UITransform, v3, Vec3 } from 'cc';
import { GameModel } from '../model/GameModel';
import { Tools } from '../common/Tools';
import { AudioTools } from '../common/AudioTools';
import { PlatformManager } from '../manager/PlatformManager';
import { AdManager } from '../ads/AdManager';
const { ccclass, property } = _decorator;

@ccclass('winTs')
export class winTs extends Component {
    @property(Node)
    mask: Node = null
    @property(Node)
    light: Node = null
    @property(Node)
    people: Node = null
    @property(Node)
    complete: Node = null
    start() {
        AudioTools._ins.stopAudio("bgm")
        GameModel._ins.bgTs.levelLable.active = false
        GameModel._ins.bgTs.auto.active = false
        GameModel._ins.bgTs.chongZhi.active = false
        this.mask.getComponent(UITransform).height = GameModel._ins.bgTs.gameHeight
        this.mask.getComponent(UITransform).width = GameModel._ins.bgTs.gameWidth
        this.light.scale = v3(0, 0, 0)
        this.people.children[0].scale = v3(0, 0, 0)
        this.people.children[1].scale = v3(0, 0, 0)
        this.people.children[2].scale = v3(0, 0, 0)
        this.people.children[3].scale = v3(0, 0, 0)
        this.people.children[4].scale = v3(0, 0, 0)
        this.complete.scale = v3(0, 0, 0)
        this.action()
        AudioTools._ins.playAudio("success")
        for (let child of GameModel._ins.bgTs.savePan) {
            if (child) {
                child.destroy()
            }
        }
        for (let child of GameModel._ins.bgTs.saveZancun) {
            if (child) {
                child.destroy()
            }
        }
    }
    update(deltaTime: number) {

    }

    action() {
        tween(this.light)
            .by(1, { angle: -180 })
            .repeatForever()
            .start()
        tween(this.light)
            .to(0.3, { scale: v3(1, 1, 1) })
            .call(() => {
                this.createCaidai()
                tween(this.complete)
                    .to(0.3, { scale: v3(1.4, 1.4, 1.4) })
                    .call(() => {
                        tween(this.people.children[0])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[0])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[1])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[1])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[2])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[2])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[3])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[3])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[4])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[4])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                    })
                    .to(0.2, { scale: v3(1.2, 1.2, 1.2) })
                    .delay(1)
                    .call(() => {
                        // 遍历所有子节点,除了mask以外都缩小消失
                        this.node.children.forEach(child => {
                            if (child.name !== 'mask') {
                                tween(child)
                                    .to(0.3, { scale: v3(0, 0, 0) }, { easing: 'sineIn' })
                                    .call(() => {
                                        this.node.destroy()
                                        AdManager.showIntersAd()
                                        this.nextLevel();
                                    })
                                    .start();
                            }
                        });
                    })
                    .start()
            })
            .start()

    }
    createCaidai() {
        AudioTools._ins.playAudio("cd")
        for (let i = 0; i < 60; i++) {
            let num = Tools.random(1, 8, true)
            let caidai = Tools.newPrefab("cd")
            caidai.setParent(this.node)
            caidai.setPosition(v3(0, 0))
            Tools.setSpriteFrame(caidai, "cd" + num.toString())
        }
    }
    nextLevel() {
        GameModel._ins.bgTs.levelLable.active = true
        GameModel._ins.bgTs.auto.active = true
        GameModel._ins.bgTs.chongZhi.active = true
        GameModel._ins.bgTs.level++
        PlatformManager.setStorage("binglevel", GameModel._ins.bgTs.level)
        GameModel._ins.bgTs.levelLable.children[0].getComponent(Label).string = Number(GameModel._ins.bgTs.level).toString()
        GameModel._ins.bgTs.orders = []
        GameModel._ins.bgTs.pileConfigurations = []
        GameModel._ins.bgTs.isMove = false;
        GameModel._ins.bgTs.disPlayIndex = []
        GameModel._ins.bgTs.hasWon = false
        GameModel._ins.bgTs.bingLuoNum = 0
        GameModel._ins.bgTs.bingNum = 0
        GameModel._ins.bgTs.bingColor = 0
        GameModel._ins.bgTs.saveBing = []
        GameModel._ins.bgTs.saveZancunBing = []
        GameModel._ins.bgTs.savePeople = [null, null, null]
        GameModel._ins.bgTs.saveTip = [null, null, null, null]
        GameModel._ins.bgTs.saveTipFloor = [null, null, null, null]
        GameModel._ins.bgTs.tipPos = []
        GameModel._ins.bgTs.savePanPos = []
        GameModel._ins.bgTs.saveZancunPos = []
        GameModel._ins.bgTs.savePeoplePos = []
        GameModel._ins.bgTs.savePan = []
        GameModel._ins.bgTs.saveZancun = []
        GameModel._ins.bgTs.saveTipColors = [null, null, null, null]
        GameModel._ins.bgTs.zanCunAnimationState = [0, 0, 0, 0, 0, 0]
        GameModel._ins.bgTs.createPanPos();
        GameModel._ins.bgTs.createZancunPos();
        GameModel._ins.bgTs.createPeoplePos();
        GameModel._ins.bgTs.createPan();
        GameModel._ins.bgTs.createZancun();
        GameModel._ins.bgTs.setLevel();
        AudioTools._ins.playBG("bgm")

    }
}
