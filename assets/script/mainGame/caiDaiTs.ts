import { _decorator, Component, Node, v3, tween, Vec2, math, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('cai_dai')
export class cai_dai extends Component {
    start() {
        let isLeft = true;
        this.node.scale = v3(Math.random() * 0.6 + 0.3, Math.random() * 0.6 + 0.3, 1);

        if (Math.random() < 0.5) {
            this.node.setPosition(-520, -400, 0);
        } else {
            isLeft = false;
            this.node.setPosition(520, -400, 0);
        }

        let time = Math.random() * 0.08 + 0.17;
        let angle = 90;

        if (isLeft) {
            angle -= Math.random() * 45;
        } else {
            angle += Math.random() * 45;
        }

        // 使用Vec2替代cc.v2
        let vec = new Vec2(Math.cos(angle * Math.PI / 180), Math.sin(angle * Math.PI / 180));
        vec.multiplyScalar(Math.random() * 1200);

        // 使用tween替代cc.spawn和cc.moveBy
        tween(this.node)
            .by(time + 0.05, { position: v3(vec.x, vec.y, 0) }, { easing: 'quadOut' })
            .by(10, { position: v3(0, -1000 + Math.random() * 400 - 200, 0) })
            .start();

        let randomAngle = Math.random() * 15 + 15;
        tween(this.node)
            .by(0.1, { angle: randomAngle })
            .union()
            .repeatForever()
            .start();

        if (Math.random() < 0.8) {
            let targetScaleX = this.node.scale.x;
            tween(this.node)
                .delay(time)
                .to(time * 2, { scale: v3(-targetScaleX, this.node.scale.y, this.node.scale.z) })
                .start();
        }

        tween(this.node)
            .delay(Math.random() * 0.2 + 1)
            .call(() => {
                tween(this.node.getComponent(UIOpacity))
                    .to(time * 2, { opacity: 0 })
                    .call(() => {
                        this.node.destroy();
                    })
                    .start()
            })
            .start();
    }

    update(deltaTime: number) {
        // 不需要在update中添加内容
    }
}

