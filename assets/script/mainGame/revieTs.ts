import { _decorator, Component, EventTouch, Node, settings, tween, UI, UITransform, v3 } from 'cc';
import { GameModel } from '../model/GameModel';
const { ccclass, property } = _decorator;

@ccclass('revieTs')
export class revieTs extends Component {
    @property(Node)
    maskNode: Node = null
    @property(Node)
    bgNode: Node = null
    @property(Node)
    yesBtn: Node = null
    @property(Node)
    closeBtn: Node = null
    @property(Node)
    people: Node = null
    @property(Node)
    qiPao: Node = null
    @property(Node)
    TalkKuang: Node = null
    start() {
        this.maskNode.getComponent(UITransform).width = GameModel._ins.bgTs.gameWidth
        this.maskNode.getComponent(UITransform).height = GameModel._ins.bgTs.gameHeight
        this.closeBtn.on(Node.EventType.TOUCH_START, this.closeFunc, this);
        this.yesBtn.on(Node.EventType.TOUCH_START, this.adYes, this)
    }

    update(deltaTime: number) {

    }
    /**关闭按钮 */
    closeFunc() {
        tween(this.closeBtn)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
                this.close()
            })
            .start()
    }

    close() {
        this.node.destroy()
        GameModel._ins.mainGame.gameEnd()
    }
    /**同意按钮 */
    adYes() {
        tween(this.yesBtn)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
                this.agineGame()
            })
            .start()
    }
    agineGame() {
        this.node.destroy()
        GameModel._ins.bgTs.isRevie = true
        GameModel._ins.bgTs.isDead = false
        GameModel._ins.bgTs.revive()
    }



}

