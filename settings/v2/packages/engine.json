{"__version__": "1.0.10", "modules": {"cache": {"base": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": true}, "gfx-webgpu": {"_value": false}, "animation": {"_value": true}, "skeletal-animation": {"_value": false}, "3d": {"_value": true}, "meshopt": {"_value": false}, "2d": {"_value": true}, "xr": {"_value": false}, "ui": {"_value": true}, "particle": {"_value": false}, "physics": {"_value": true, "_option": "physics-ammo", "_flags": {"physics-ammo": {"LOAD_BULLET_MANUALLY": false}, "physics-physx": {"LOAD_PHYSX_MANUALLY": false}}}, "physics-ammo": {"_value": false, "_flags": {"physics-ammo": {"LOAD_BULLET_MANUALLY": false}}}, "physics-cannon": {"_value": false}, "physics-physx": {"_value": false, "_flags": {"physics-physx": {"LOAD_PHYSX_MANUALLY": false}}}, "physics-builtin": {"_value": false}, "physics-2d": {"_value": true, "_option": "physics-2d-box2d", "_flags": {"physics-2d-box2d-wasm": {"LOAD_BOX2D_MANUALLY": false}}}, "physics-2d-box2d": {"_value": false}, "physics-2d-box2d-wasm": {"_value": false, "_flags": {"physics-2d-box2d-wasm": {"LOAD_BOX2D_MANUALLY": false}}}, "physics-2d-builtin": {"_value": false}, "intersection-2d": {"_value": true}, "primitive": {"_value": true}, "profiler": {"_value": true}, "occlusion-query": {"_value": false}, "geometry-renderer": {"_value": false}, "debug-renderer": {"_value": false}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": true}, "webview": {"_value": true}, "tween": {"_value": true}, "websocket": {"_value": true}, "websocket-server": {"_value": true}, "terrain": {"_value": false}, "light-probe": {"_value": false}, "tiled-map": {"_value": true}, "vendor-google": {"_value": false}, "spine": {"_value": true, "_flags": {"spine": {"LOAD_SPINE_MANUALLY": false}}}, "dragon-bones": {"_value": true}, "marionette": {"_value": false}, "procedural-animation": {"_value": false}, "custom-pipeline-post-process": {"_value": false}, "render-pipeline": {"_value": true, "_option": "legacy-pipeline"}, "custom-pipeline": {"_value": true}, "legacy-pipeline": {"_value": false}, "graphics": {"_value": true}}, "includeModules": ["2d", "3d", "animation", "audio", "base", "dragon-bones", "gfx-webgl", "gfx-webgl2", "intersection-2d", "legacy-pipeline", "particle-2d", "physics-2d-box2d", "physics-ammo", "primitive", "profiler", "spine", "tiled-map", "tween", "ui", "video", "websocket", "websocket-server", "webview"], "noDeprecatedFeatures": {"value": false, "version": ""}, "flags": {}}}