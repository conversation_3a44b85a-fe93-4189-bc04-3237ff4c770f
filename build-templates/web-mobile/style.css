html {
  -ms-touch-action: none;
}

body, canvas, div {
  display: block;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Remove spin of input type number */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

body {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  border: 0;
  margin: 0;

  cursor: default;
  color: #ffffff;
  background-color: #ffffff;

  text-align: center;
  font-family: Helvetica, Verdana, Arial, sans-serif;

  display: flex;
  flex-direction: column;
}

canvas {
  background-color: rgb(255, 255, 255);
}

#GameDiv, #Cocos3dGameContainer, #GameCanvas {
  width: 100%;
  height: 100%;
}
#splash {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  background-size: 45%;
}

.progress-bar {
  position: absolute;
  left: 20%;
  top: 53%;
  height: 8.5px;
  padding: 0px;
  width: 60%;
  background: #c5c5c5;
  border-radius: 7px;
}

.progress-bar span {
  display: block;
  height: 100%;
  border-radius: 7px;
  transition: width .4s ease-in-out;
  background-color: #ffe451;
}

.stripes span {
  background-size: 30px 30px;
  background-image: linear-gradient(135deg, rgba(253, 216, 67, 1) 25%, transparent 25%,
      transparent 50%, rgba(253, 216, 67, 1) 50%, rgba(253, 216, 67, 1) 75%,
      transparent 75%, transparent);

  animation: animate-stripes 1s linear infinite;
}

@keyframes animate-stripes {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 60px 0;
  }
}
:root {
  --safe-top: env(safe-area-inset-top);
  --safe-right: env(safe-area-inset-right);
  --safe-bottom: env(safe-area-inset-bottom);
  --safe-left: env(safe-area-inset-left);
}
