[{"__type__": "cc.Prefab", "_name": "OverUI", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "OverUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}, {"__id__": 84}], "_prefab": {"__id__": 86}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bgColor", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acM2Q/fRtLwZuE91UXa+FG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "56a35c4e-18ad-4795-86ad-f11bb3ebaa5a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78huw7eAdAyYqfxygspFTc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2, "_originalHeight": 2, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebkODhlJ5Ly7ht/4eRmdX+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_opacity": 180, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff/VWyD5dLupDTgLeBMD4w"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7ap/IFsVhC27m/oi3Heq2G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "viewNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 25}, {"__id__": 39}, {"__id__": 45}, {"__id__": 57}, {"__id__": 71}], "_active": true, "_components": [{"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "椭圆 700", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "socreLab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6.904999999999973, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 61.82, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aDIcpBjJOPZUaA8k7tuE7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 80, "_fontSize": 80, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "c1a9ccac-3a99-4ec6-a2b7-230e88d142de", "__expectedType__": "cc.LabelAtlas"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06KMtnVo1Kg4qWdZre7R2M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "faaK4+AN9HsaKutgatbiY2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 198, "height": 198}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98TBAhsaFGuYeG97KJ1oos"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6QyW3zJJKwL3ELJ34simO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8CjoCeGlIoapkFB85koTG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "maxScoreLab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 26}], "_active": false, "_components": [{"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": 25, "y": 150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "maxIcon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": -86.96871093749999, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 28}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bBYV1DIpD54TgBqYYsGGc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 30}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b914cf0-224a-4a7a-93cc-4ab3906fbeec@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6a7H0hXphJJacu8+KdOGN2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 32}, "_alignFlags": 8, "_target": null, "_left": -59.915, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54dy7ELqZElr+KRSl4Jdjc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dhAMZWbtGdqfa5cDjlyBo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 100.107421875, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43LwWioiZHPoDcNR0BBsLd"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "999", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99Y6CX1GhMEJZe27H1VMj2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71Ucx4zV5BC4C3TZiSxRHd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "infoLab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": false, "_components": [{"__id__": 40}, {"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1.234, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 404.5166015625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4buyExbydONrSwU3NQqiOF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 43}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "击败了全球100%的玩家！", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aHetxgVNNP5YtREEVinfk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbAkvSO1RKoJYBZa2KIE11", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "startBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 46}], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -321.33, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [{"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 141}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2ym4wRSZMHL5qzJt9Ling"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 50}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "54d2b934-6ead-4401-a1dc-4275ee0ac006@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1JRYHeyRD27Wp14bX/z3Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09LRYxkalGyLuRoSwKVW4z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 53}, "_contentSize": {"__type__": "cc.Size", "width": 421, "height": 155}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "339WZA8HNAip2wi/LQnJCO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 55}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 45}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aPPJZfp5BNoIQ2yKDBjFx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ak4wB2SlPsrcw5usSiw8v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 58}], "_active": true, "_components": [{"__id__": 64}, {"__id__": 66}, {"__id__": 68}], "_prefab": {"__id__": 70}, "_lpos": {"__type__": "cc.Vec3", "x": 2.114, "y": -603.591, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnBg", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}], "_prefab": {"__id__": 63}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 60}, "_contentSize": {"__type__": "cc.Size", "width": 421, "height": 155}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55J8T7NaJGtYRtquubZV75"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 62}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "78296a44-9baa-4697-bd9d-7b89699e043e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dat18jzhESJRReU1ZIWeG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "977wZ6iStHmLN9I0gG0avY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 65}, "_contentSize": {"__type__": "cc.Size", "width": 421, "height": 155}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7oSHWX29LEYkQ4Ev0xIt2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 67}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 57}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75kgo/Qh1BXbB1+s1X910M"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 69}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 576.091, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88FaunB05I+qLm67tuMYsr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89Is10BVxB4Yb+gNQW+owo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GAME OVER", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 280.251, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 73}, "_contentSize": {"__type__": "cc.Size", "width": 696, "height": 186}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fXNfVmRtALaHy0UAZQesM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 75}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d7b75c28-70c8-430d-b7d7-dc231c052049@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1CVpGEfpIl5F69JXkfKeC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b33nyyL0tLoJVS9iUX5X6m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 78}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26inYBFV9IuLccs/BmpSvl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98x9nSCmFGHKeZuk2GtA6V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "37f48mOpsZDIKuk5N0nbpWc", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 81}, "bgSpr": {"__id__": 2}, "viewNode": {"__id__": 12}, "scoreLab": {"__id__": 17}, "maxScoreLab": {"__id__": 36}, "infoText": {"__id__": 42}, "moreBtn": {"__id__": 57}, "againBtn": {"__id__": 45}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3IYo7S4lKx7DZt33CzqQ7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 83}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4674fWl/ZLo70VuPSzEDT7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 85}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccYKPIGqVOCL6RGVVL71nf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adXKxY1ZVNR7K8iXt74BRl", "instance": null, "targetOverrides": null}]