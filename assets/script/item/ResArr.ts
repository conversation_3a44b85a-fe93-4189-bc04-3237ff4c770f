
import { _decorator, Component, SpriteFrame, Prefab, AudioClip, Texture2D, Material, assertID, assetManager, Asset, Sprite } from 'cc';
import { Tools } from '../common/Tools';
import { ResModel } from '../model/ResModel';
const { ccclass, property } = _decorator;

@ccclass('ResArr')
export class ResArr extends Component {
    @property([SpriteFrame])
    public SpriteFrameArr: Array<SpriteFrame> = [];
    @property([Prefab])
    public PrefabArr: Array<Prefab> = [];
    @property([AudioClip])
    public audiosArr: Array<AudioClip> = [];

    @property([Material])
    public MaterialArr: Array<Material> = [];
    @property([SpriteFrame])
    public Texture2DArr: Array<SpriteFrame> = [];

    onLoad() {
        this.addAudio();
        this.addPrefabs();
        this.addSpriteFrame();
        this.addMaterial();

        this.addTexture();
    }
    /** 添加音效文件  到Tools字典里面 */
    addAudio() {
        for (let i = 0; i < this.audiosArr.length; i++) {
            if (this.audiosArr[i]) {
                const element = this.audiosArr[i];
                ResModel._ins.AudioClipDic.set(element.name, element);
            }
        }
    }
    /** 添加图片文件  到Tools字典里面 */
    addSpriteFrame() {
        for (let i = 0; i < this.SpriteFrameArr.length; i++) {
            if (this.SpriteFrameArr[i]) {
                const element = this.SpriteFrameArr[i];
                ResModel._ins.SpriteFrameDic.set(element.name, element);
            }
        }
    }
    /** 添加预制体文件  到Tools字典里面 */
    addPrefabs() {
        for (let i = 0; i < this.PrefabArr.length; i++) {
            if (this.PrefabArr[i]) {
                const element = this.PrefabArr[i];
                ResModel._ins.PrefabDic.set(element.data.name, element);
            }
        }
    }
    /** 添加材质文件  到Tools字典里面 */
    addMaterial() {
        for (let i = 0; i < this.MaterialArr.length; i++) {
            if (this.MaterialArr[i]) {
                const element = this.MaterialArr[i];
                ResModel._ins.MaterialDic.set(element.name, element);
            }
        }
    }
    /** 添加Texture文件  到Tools字典里面 */
    addTexture() {
        for (let i = 0; i < this.Texture2DArr.length; i++) {
            if (this.Texture2DArr[i]) {
                const element = this.Texture2DArr[i];
                ResModel._ins.TextureDic.set(element.name, element.texture as Texture2D);
            }
        }
    }
}